[2025-08-04 12:50:35] [INFO] === 新的访问开始 ===
[2025-08-04 12:50:35] [INFO] 请求URI: /shop.php
[2025-08-04 12:50:35] [INFO] 请求方法: GET
[2025-08-04 12:50:35] [INFO] GET参数: []
[2025-08-04 12:50:35] [INFO] POST参数: []
[2025-08-04 12:50:35] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:50:35] [INFO] 客户端IP: *************
[2025-08-04 12:50:35] [INFO] 解析参数 - sj: '', sp: ''
[2025-08-04 12:50:35] [ERROR] 参数验证失败：sj和sp都为空，返回404
[2025-08-04 12:50:38] [INFO] === 新的访问开始 ===
[2025-08-04 12:50:38] [INFO] 请求URI: /shop.php
[2025-08-04 12:50:38] [INFO] 请求方法: GET
[2025-08-04 12:50:38] [INFO] GET参数: []
[2025-08-04 12:50:38] [INFO] POST参数: []
[2025-08-04 12:50:38] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:50:38] [INFO] 客户端IP: *************
[2025-08-04 12:50:38] [INFO] 解析参数 - sj: '', sp: ''
[2025-08-04 12:50:38] [ERROR] 参数验证失败：sj和sp都为空，返回404
[2025-08-04 12:50:51] [INFO] === 新的访问开始 ===
[2025-08-04 12:50:51] [INFO] 请求URI: /shop.php?sj=1221
[2025-08-04 12:50:51] [INFO] 请求方法: GET
[2025-08-04 12:50:51] [INFO] GET参数: {"sj":"1221"}
[2025-08-04 12:50:51] [INFO] POST参数: []
[2025-08-04 12:50:51] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:50:51] [INFO] 客户端IP: *************
[2025-08-04 12:50:51] [INFO] 解析参数 - sj: '1221', sp: ''
[2025-08-04 12:50:51] [INFO] 页面类型: merchant
[2025-08-04 12:50:51] [INFO] 商户ID: '1221'
[2025-08-04 12:50:51] [INFO] 商品ID: ''
[2025-08-04 12:51:00] [INFO] === 新的访问开始 ===
[2025-08-04 12:51:00] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:51:00] [INFO] 请求方法: GET
[2025-08-04 12:51:00] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:51:00] [INFO] POST参数: []
[2025-08-04 12:51:00] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:51:00] [INFO] 客户端IP: *************
[2025-08-04 12:51:00] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:51:00] [INFO] 页面类型: merchant
[2025-08-04 12:51:00] [INFO] 商户ID: '8034567958'
[2025-08-04 12:51:00] [INFO] 商品ID: ''
[2025-08-04 12:51:43] [INFO] === 新的访问开始 ===
[2025-08-04 12:51:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:51:43] [INFO] 请求方法: GET
[2025-08-04 12:51:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:51:43] [INFO] POST参数: []
[2025-08-04 12:51:43] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:51:43] [INFO] 客户端IP: *************
[2025-08-04 12:51:43] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:51:43] [INFO] 页面类型: merchant
[2025-08-04 12:51:43] [INFO] 商户ID: '8034567958'
[2025-08-04 12:51:43] [INFO] 商品ID: ''
[2025-08-04 12:51:48] [INFO] === 新的访问开始 ===
[2025-08-04 12:51:48] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:51:48] [INFO] 请求方法: GET
[2025-08-04 12:51:48] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:51:48] [INFO] POST参数: []
[2025-08-04 12:51:48] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:51:48] [INFO] 客户端IP: *************
[2025-08-04 12:51:48] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:51:48] [INFO] 页面类型: merchant
[2025-08-04 12:51:48] [INFO] 商户ID: '8034567958'
[2025-08-04 12:51:48] [INFO] 商品ID: ''
[2025-08-04 12:51:50] [INFO] === 新的访问开始 ===
[2025-08-04 12:51:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:51:50] [INFO] 请求方法: GET
[2025-08-04 12:51:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:51:50] [INFO] POST参数: []
[2025-08-04 12:51:50] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:51:50] [INFO] 客户端IP: *************
[2025-08-04 12:51:50] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:51:50] [INFO] 页面类型: merchant
[2025-08-04 12:51:50] [INFO] 商户ID: '8034567958'
[2025-08-04 12:51:50] [INFO] 商品ID: ''
[2025-08-04 12:52:36] [INFO] === 新的访问开始 ===
[2025-08-04 12:52:36] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:52:36] [INFO] 请求方法: GET
[2025-08-04 12:52:36] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:52:36] [INFO] POST参数: []
[2025-08-04 12:52:36] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:52:36] [INFO] 客户端IP: *************
[2025-08-04 12:52:36] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:52:36] [INFO] 页面类型: merchant
[2025-08-04 12:52:36] [INFO] 商户ID: '8034567958'
[2025-08-04 12:52:36] [INFO] 商品ID: ''
[2025-08-04 12:52:39] [INFO] === 新的访问开始 ===
[2025-08-04 12:52:39] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:52:39] [INFO] 请求方法: GET
[2025-08-04 12:52:39] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:52:39] [INFO] POST参数: []
[2025-08-04 12:52:39] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:52:39] [INFO] 客户端IP: *************
[2025-08-04 12:52:39] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:52:39] [INFO] 页面类型: merchant
[2025-08-04 12:52:39] [INFO] 商户ID: '8034567958'
[2025-08-04 12:52:39] [INFO] 商品ID: ''
[2025-08-04 12:54:29] [INFO] === 新的访问开始 ===
[2025-08-04 12:54:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:54:29] [INFO] 请求方法: GET
[2025-08-04 12:54:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:54:29] [INFO] POST参数: []
[2025-08-04 12:54:29] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:54:29] [INFO] 客户端IP: **************
[2025-08-04 12:54:29] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:54:29] [INFO] 页面类型: merchant
[2025-08-04 12:54:29] [INFO] 商户ID: '8034567958'
[2025-08-04 12:54:29] [INFO] 商品ID: ''
[2025-08-04 12:54:43] [INFO] === 新的访问开始 ===
[2025-08-04 12:54:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:54:43] [INFO] 请求方法: GET
[2025-08-04 12:54:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:54:43] [INFO] POST参数: []
[2025-08-04 12:54:43] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:54:43] [INFO] 客户端IP: **************
[2025-08-04 12:54:43] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:54:43] [INFO] 页面类型: merchant
[2025-08-04 12:54:43] [INFO] 商户ID: '8034567958'
[2025-08-04 12:54:43] [INFO] 商品ID: ''
[2025-08-04 12:55:37] [INFO] === 新的访问开始 ===
[2025-08-04 12:55:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:55:37] [INFO] 请求方法: GET
[2025-08-04 12:55:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:55:37] [INFO] POST参数: []
[2025-08-04 12:55:37] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:55:37] [INFO] 客户端IP: **************
[2025-08-04 12:55:37] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:55:37] [INFO] 页面类型: merchant
[2025-08-04 12:55:37] [INFO] 商户ID: '8034567958'
[2025-08-04 12:55:37] [INFO] 商品ID: ''
[2025-08-04 13:25:47] [INFO] === 新的访问开始 ===
[2025-08-04 13:25:47] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:25:47] [INFO] 请求方法: GET
[2025-08-04 13:25:47] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:25:47] [INFO] POST参数: []
[2025-08-04 13:25:47] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:25:47] [INFO] 客户端IP: ************
[2025-08-04 13:25:47] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 13:25:47] [INFO] 页面类型: merchant
[2025-08-04 13:25:47] [INFO] 商户ID: '8034567958'
[2025-08-04 13:25:47] [INFO] 商品ID: ''
[2025-08-04 13:25:47] [INFO] 订单ID: ''
[2025-08-04 13:29:30] [INFO] === 新的访问开始 ===
[2025-08-04 13:29:30] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:29:30] [INFO] 请求方法: GET
[2025-08-04 13:29:30] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:29:30] [INFO] POST参数: []
[2025-08-04 13:29:30] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:29:30] [INFO] 客户端IP: ************
[2025-08-04 13:29:30] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 13:29:30] [INFO] 页面类型: merchant
[2025-08-04 13:29:30] [INFO] 商户ID: '8034567958'
[2025-08-04 13:29:30] [INFO] 商品ID: ''
[2025-08-04 13:29:30] [INFO] 订单ID: ''
[2025-08-04 13:39:23] [INFO] === 新的访问开始 ===
[2025-08-04 13:39:23] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:39:23] [INFO] 请求方法: GET
[2025-08-04 13:39:23] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:39:23] [INFO] POST参数: []
[2025-08-04 13:39:23] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:39:23] [INFO] 客户端IP: **************
[2025-08-04 13:39:23] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 13:39:23] [INFO] 页面类型: merchant
[2025-08-04 13:39:23] [INFO] 商户ID: '8034567958'
[2025-08-04 13:39:23] [INFO] 商品ID: ''
[2025-08-04 13:39:23] [INFO] 订单ID: ''
[2025-08-04 13:39:27] [INFO] === 新的访问开始 ===
[2025-08-04 13:39:27] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:39:27] [INFO] 请求方法: GET
[2025-08-04 13:39:27] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:39:27] [INFO] POST参数: []
[2025-08-04 13:39:27] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:39:27] [INFO] 客户端IP: **************
[2025-08-04 13:39:27] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 13:39:27] [INFO] 页面类型: merchant
[2025-08-04 13:39:27] [INFO] 商户ID: '8034567958'
[2025-08-04 13:39:27] [INFO] 商品ID: ''
[2025-08-04 13:39:27] [INFO] 订单ID: ''
[2025-08-04 13:48:43] [INFO] === 新的访问开始 ===
[2025-08-04 13:48:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:48:43] [INFO] 请求方法: GET
[2025-08-04 13:48:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:48:43] [INFO] POST参数: []
[2025-08-04 13:48:43] [INFO] User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138 Version/11.1.1 Safari/605.1.15
[2025-08-04 13:48:43] [INFO] 客户端IP: ************
[2025-08-04 13:48:43] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 13:48:43] [INFO] 页面类型: merchant
[2025-08-04 13:48:43] [INFO] 商户ID: '8034567958'
[2025-08-04 13:48:43] [INFO] 商品ID: ''
[2025-08-04 13:48:43] [INFO] 订单ID: ''
[2025-08-04 13:50:06] [INFO] === 新的访问开始 ===
[2025-08-04 13:50:06] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:50:06] [INFO] 请求方法: GET
[2025-08-04 13:50:06] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:50:06] [INFO] POST参数: []
[2025-08-04 13:50:06] [INFO] User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138 Version/11.1.1 Safari/605.1.15
[2025-08-04 13:50:06] [INFO] 客户端IP: ************
[2025-08-04 13:50:06] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 13:50:06] [INFO] 页面类型: merchant
[2025-08-04 13:50:06] [INFO] 商户ID: '8034567958'
[2025-08-04 13:50:06] [INFO] 商品ID: ''
[2025-08-04 13:50:06] [INFO] 订单ID: ''
[2025-08-04 13:51:03] [INFO] === 新的访问开始 ===
[2025-08-04 13:51:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:51:03] [INFO] 请求方法: GET
[2025-08-04 13:51:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:51:03] [INFO] POST参数: []
[2025-08-04 13:51:03] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:51:03] [INFO] 客户端IP: **************
[2025-08-04 13:51:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 13:51:03] [INFO] 页面类型: merchant
[2025-08-04 13:51:03] [INFO] 商户ID: '8034567958'
[2025-08-04 13:51:03] [INFO] 商品ID: ''
[2025-08-04 13:51:03] [INFO] 订单ID: ''
[2025-08-04 13:51:08] [INFO] === 新的访问开始 ===
[2025-08-04 13:51:08] [INFO] 请求URI: /shop.php?sp=8034567958
[2025-08-04 13:51:08] [INFO] 请求方法: GET
[2025-08-04 13:51:08] [INFO] GET参数: {"sp":"8034567958"}
[2025-08-04 13:51:08] [INFO] POST参数: []
[2025-08-04 13:51:08] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:51:08] [INFO] 客户端IP: **************
[2025-08-04 13:51:08] [INFO] 解析参数 - sj: '', sp: '8034567958', dd: ''
[2025-08-04 13:51:08] [INFO] 页面类型: product
[2025-08-04 13:51:08] [INFO] 商户ID: ''
[2025-08-04 13:51:08] [INFO] 商品ID: '8034567958'
[2025-08-04 13:51:08] [INFO] 订单ID: ''
[2025-08-04 13:51:11] [INFO] === 新的访问开始 ===
[2025-08-04 13:51:11] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 13:51:11] [INFO] 请求方法: GET
[2025-08-04 13:51:11] [INFO] GET参数: {"sp":"23"}
[2025-08-04 13:51:11] [INFO] POST参数: []
[2025-08-04 13:51:11] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:51:11] [INFO] 客户端IP: **************
[2025-08-04 13:51:11] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 13:51:11] [INFO] 页面类型: product
[2025-08-04 13:51:11] [INFO] 商户ID: ''
[2025-08-04 13:51:11] [INFO] 商品ID: '23'
[2025-08-04 13:51:11] [INFO] 订单ID: ''
[2025-08-04 13:51:24] [INFO] === 新的访问开始 ===
[2025-08-04 13:51:24] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 13:51:24] [INFO] 请求方法: GET
[2025-08-04 13:51:24] [INFO] GET参数: {"sp":"23"}
[2025-08-04 13:51:24] [INFO] POST参数: []
[2025-08-04 13:51:24] [INFO] User-Agent: TelegramBot (like TwitterBot)
[2025-08-04 13:51:24] [INFO] 客户端IP: **************
[2025-08-04 13:51:24] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 13:51:24] [INFO] 页面类型: product
[2025-08-04 13:51:24] [INFO] 商户ID: ''
[2025-08-04 13:51:24] [INFO] 商品ID: '23'
[2025-08-04 13:51:24] [INFO] 订单ID: ''
[2025-08-04 13:53:29] [INFO] === 新的访问开始 ===
[2025-08-04 13:53:29] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 13:53:29] [INFO] 请求方法: GET
[2025-08-04 13:53:29] [INFO] GET参数: {"sp":"23"}
[2025-08-04 13:53:29] [INFO] POST参数: []
[2025-08-04 13:53:29] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:53:29] [INFO] 客户端IP: **************
[2025-08-04 13:53:29] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 13:53:29] [INFO] 页面类型: product
[2025-08-04 13:53:29] [INFO] 商户ID: ''
[2025-08-04 13:53:29] [INFO] 商品ID: '23'
[2025-08-04 13:53:29] [INFO] 订单ID: ''
[2025-08-04 13:54:04] [INFO] === 新的访问开始 ===
[2025-08-04 13:54:04] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 13:54:04] [INFO] 请求方法: GET
[2025-08-04 13:54:04] [INFO] GET参数: {"sp":"23"}
[2025-08-04 13:54:04] [INFO] POST参数: []
[2025-08-04 13:54:04] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-04 13:54:04] [INFO] 客户端IP: **************
[2025-08-04 13:54:04] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 13:54:04] [INFO] 页面类型: product
[2025-08-04 13:54:04] [INFO] 商户ID: ''
[2025-08-04 13:54:04] [INFO] 商品ID: '23'
[2025-08-04 13:54:04] [INFO] 订单ID: ''
[2025-08-04 14:23:20] [INFO] === 新的访问开始 ===
[2025-08-04 14:23:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 14:23:20] [INFO] 请求方法: GET
[2025-08-04 14:23:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 14:23:20] [INFO] POST参数: []
[2025-08-04 14:23:20] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 14; zh-cn; 21121210C Build/UKQ1.240624.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/20.0.20728
[2025-08-04 14:23:20] [INFO] 客户端IP: **************
[2025-08-04 14:23:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 14:23:20] [INFO] 页面类型: merchant
[2025-08-04 14:23:20] [INFO] 商户ID: '8034567958'
[2025-08-04 14:23:20] [INFO] 商品ID: ''
[2025-08-04 14:23:20] [INFO] 订单ID: ''
[2025-08-04 14:54:16] [INFO] === 新的访问开始 ===
[2025-08-04 14:54:16] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 14:54:16] [INFO] 请求方法: GET
[2025-08-04 14:54:16] [INFO] GET参数: {"sp":"23"}
[2025-08-04 14:54:16] [INFO] POST参数: []
[2025-08-04 14:54:16] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 8.0.0; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.3538.110 Mobile Safari/537.36
[2025-08-04 14:54:16] [INFO] 客户端IP: **************
[2025-08-04 14:54:16] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 14:54:16] [INFO] 页面类型: product
[2025-08-04 14:54:16] [INFO] 商户ID: ''
[2025-08-04 14:54:16] [INFO] 商品ID: '23'
[2025-08-04 14:54:16] [INFO] 订单ID: ''
[2025-08-04 16:04:42] [INFO] === 新的访问开始 ===
[2025-08-04 16:04:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 16:04:42] [INFO] 请求方法: GET
[2025-08-04 16:04:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 16:04:42] [INFO] POST参数: []
[2025-08-04 16:04:42] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.98 Safari/537.36 T7/13.38 languageType/0 bdh_dvt/0 bdh_de/1 bdh_ds/1 bdhonorbrowser/******* (P1 13)
[2025-08-04 16:04:42] [INFO] 客户端IP: **************
[2025-08-04 16:04:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 16:04:42] [INFO] 页面类型: merchant
[2025-08-04 16:04:42] [INFO] 商户ID: '8034567958'
[2025-08-04 16:04:42] [INFO] 商品ID: ''
[2025-08-04 16:04:42] [INFO] 订单ID: ''
[2025-08-04 16:18:25] [INFO] === 新的访问开始 ===
[2025-08-04 16:18:25] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 16:18:25] [INFO] 请求方法: GET
[2025-08-04 16:18:25] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 16:18:25] [INFO] POST参数: []
[2025-08-04 16:18:25] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 16:18:25] [INFO] 客户端IP: **************
[2025-08-04 16:18:25] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 16:18:25] [INFO] 页面类型: merchant
[2025-08-04 16:18:25] [INFO] 商户ID: '8034567958'
[2025-08-04 16:18:25] [INFO] 商品ID: ''
[2025-08-04 16:18:25] [INFO] 订单ID: ''
[2025-08-04 16:20:09] [INFO] === 新的访问开始 ===
[2025-08-04 16:20:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 16:20:09] [INFO] 请求方法: GET
[2025-08-04 16:20:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 16:20:09] [INFO] POST参数: []
[2025-08-04 16:20:09] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 16:20:09] [INFO] 客户端IP: **************
[2025-08-04 16:20:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 16:20:09] [INFO] 页面类型: merchant
[2025-08-04 16:20:09] [INFO] 商户ID: '8034567958'
[2025-08-04 16:20:09] [INFO] 商品ID: ''
[2025-08-04 16:20:09] [INFO] 订单ID: ''
[2025-08-04 16:20:46] [INFO] === 新的访问开始 ===
[2025-08-04 16:20:46] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 16:20:46] [INFO] 请求方法: GET
[2025-08-04 16:20:46] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 16:20:46] [INFO] POST参数: []
[2025-08-04 16:20:46] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 16:20:46] [INFO] 客户端IP: **************
[2025-08-04 16:20:46] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 16:20:46] [INFO] 页面类型: merchant
[2025-08-04 16:20:46] [INFO] 商户ID: '8034567958'
[2025-08-04 16:20:46] [INFO] 商品ID: ''
[2025-08-04 16:20:46] [INFO] 订单ID: ''
[2025-08-04 16:23:42] [INFO] === 新的访问开始 ===
[2025-08-04 16:23:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 16:23:42] [INFO] 请求方法: GET
[2025-08-04 16:23:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 16:23:42] [INFO] POST参数: []
[2025-08-04 16:23:42] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 16:23:42] [INFO] 客户端IP: **************
[2025-08-04 16:23:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 16:23:42] [INFO] 页面类型: merchant
[2025-08-04 16:23:42] [INFO] 商户ID: '8034567958'
[2025-08-04 16:23:42] [INFO] 商品ID: ''
[2025-08-04 16:23:42] [INFO] 订单ID: ''
[2025-08-04 16:25:08] [INFO] === 新的访问开始 ===
[2025-08-04 16:25:08] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 16:25:08] [INFO] 请求方法: GET
[2025-08-04 16:25:08] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 16:25:08] [INFO] POST参数: []
[2025-08-04 16:25:08] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 16:25:08] [INFO] 客户端IP: **************
[2025-08-04 16:25:08] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 16:25:08] [INFO] 页面类型: merchant
[2025-08-04 16:25:08] [INFO] 商户ID: '8034567958'
[2025-08-04 16:25:08] [INFO] 商品ID: ''
[2025-08-04 16:25:08] [INFO] 订单ID: ''
[2025-08-04 16:28:20] [INFO] === 新的访问开始 ===
[2025-08-04 16:28:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 16:28:20] [INFO] 请求方法: GET
[2025-08-04 16:28:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 16:28:20] [INFO] POST参数: []
[2025-08-04 16:28:20] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 16:28:20] [INFO] 客户端IP: **************
[2025-08-04 16:28:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 16:28:20] [INFO] 页面类型: merchant
[2025-08-04 16:28:20] [INFO] 商户ID: '8034567958'
[2025-08-04 16:28:20] [INFO] 商品ID: ''
[2025-08-04 16:28:20] [INFO] 订单ID: ''
[2025-08-04 16:29:31] [INFO] === 新的访问开始 ===
[2025-08-04 16:29:31] [INFO] 请求URI: /shop.php?dd=ORDER17542959174744
[2025-08-04 16:29:31] [INFO] 请求方法: GET
[2025-08-04 16:29:31] [INFO] GET参数: {"dd":"ORDER17542959174744"}
[2025-08-04 16:29:31] [INFO] POST参数: []
[2025-08-04 16:29:31] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 16:29:31] [INFO] 客户端IP: **************
[2025-08-04 16:29:31] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17542959174744'
[2025-08-04 16:29:31] [INFO] 页面类型: order
[2025-08-04 16:29:31] [INFO] 商户ID: ''
[2025-08-04 16:29:31] [INFO] 商品ID: ''
[2025-08-04 16:29:31] [INFO] 订单ID: 'ORDER17542959174744'
[2025-08-04 16:47:49] [INFO] === 新的访问开始 ===
[2025-08-04 16:47:49] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 16:47:49] [INFO] 请求方法: GET
[2025-08-04 16:47:49] [INFO] GET参数: {"sp":"23"}
[2025-08-04 16:47:49] [INFO] POST参数: []
[2025-08-04 16:47:49] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-04 16:47:49] [INFO] 客户端IP: **************
[2025-08-04 16:47:49] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 16:47:49] [INFO] 页面类型: product
[2025-08-04 16:47:49] [INFO] 商户ID: ''
[2025-08-04 16:47:49] [INFO] 商品ID: '23'
[2025-08-04 16:47:49] [INFO] 订单ID: ''
[2025-08-04 16:47:59] [INFO] === 新的访问开始 ===
[2025-08-04 16:47:59] [INFO] 请求URI: /shop.php
[2025-08-04 16:47:59] [INFO] 请求方法: GET
[2025-08-04 16:47:59] [INFO] GET参数: []
[2025-08-04 16:47:59] [INFO] POST参数: []
[2025-08-04 16:47:59] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; HUAWEI P30 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36
[2025-08-04 16:47:59] [INFO] 客户端IP: ************
[2025-08-04 16:47:59] [INFO] 解析参数 - sj: '', sp: '', dd: ''
[2025-08-04 16:47:59] [ERROR] 参数验证失败：sj、sp和dd都为空，返回404
[2025-08-04 17:05:14] [INFO] === 新的访问开始 ===
[2025-08-04 17:05:14] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 17:05:14] [INFO] 请求方法: GET
[2025-08-04 17:05:14] [INFO] GET参数: {"sp":"23"}
[2025-08-04 17:05:14] [INFO] POST参数: []
[2025-08-04 17:05:14] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.169 Mobile Safari/537.36
[2025-08-04 17:05:14] [INFO] 客户端IP: **************
[2025-08-04 17:05:14] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 17:05:14] [INFO] 页面类型: product
[2025-08-04 17:05:14] [INFO] 商户ID: ''
[2025-08-04 17:05:14] [INFO] 商品ID: '23'
[2025-08-04 17:05:14] [INFO] 订单ID: ''
[2025-08-04 17:44:05] [INFO] === 新的访问开始 ===
[2025-08-04 17:44:05] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 17:44:05] [INFO] 请求方法: GET
[2025-08-04 17:44:05] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 17:44:05] [INFO] POST参数: []
[2025-08-04 17:44:05] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.71 Mobile Safari/537.36
[2025-08-04 17:44:05] [INFO] 客户端IP: ************
[2025-08-04 17:44:05] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 17:44:05] [INFO] 页面类型: merchant
[2025-08-04 17:44:05] [INFO] 商户ID: '8034567958'
[2025-08-04 17:44:05] [INFO] 商品ID: ''
[2025-08-04 17:44:05] [INFO] 订单ID: ''
[2025-08-04 17:50:18] [INFO] === 新的访问开始 ===
[2025-08-04 17:50:18] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 17:50:18] [INFO] 请求方法: GET
[2025-08-04 17:50:18] [INFO] GET参数: {"sp":"23"}
[2025-08-04 17:50:18] [INFO] POST参数: []
[2025-08-04 17:50:18] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-04 17:50:18] [INFO] 客户端IP: **************
[2025-08-04 17:50:18] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 17:50:18] [INFO] 页面类型: product
[2025-08-04 17:50:18] [INFO] 商户ID: ''
[2025-08-04 17:50:18] [INFO] 商品ID: '23'
[2025-08-04 17:50:18] [INFO] 订单ID: ''
[2025-08-04 17:51:28] [INFO] === 新的访问开始 ===
[2025-08-04 17:51:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 17:51:28] [INFO] 请求方法: GET
[2025-08-04 17:51:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 17:51:28] [INFO] POST参数: []
[2025-08-04 17:51:28] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 17:51:28] [INFO] 客户端IP: ************
[2025-08-04 17:51:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 17:51:28] [INFO] 页面类型: merchant
[2025-08-04 17:51:28] [INFO] 商户ID: '8034567958'
[2025-08-04 17:51:28] [INFO] 商品ID: ''
[2025-08-04 17:51:28] [INFO] 订单ID: ''
[2025-08-04 17:51:42] [INFO] === 新的访问开始 ===
[2025-08-04 17:51:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 17:51:42] [INFO] 请求方法: GET
[2025-08-04 17:51:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 17:51:42] [INFO] POST参数: []
[2025-08-04 17:51:42] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 17:51:42] [INFO] 客户端IP: ************
[2025-08-04 17:51:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 17:51:42] [INFO] 页面类型: merchant
[2025-08-04 17:51:42] [INFO] 商户ID: '8034567958'
[2025-08-04 17:51:42] [INFO] 商品ID: ''
[2025-08-04 17:51:42] [INFO] 订单ID: ''
[2025-08-04 17:53:43] [INFO] === 新的访问开始 ===
[2025-08-04 17:53:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 17:53:43] [INFO] 请求方法: GET
[2025-08-04 17:53:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 17:53:43] [INFO] POST参数: []
[2025-08-04 17:53:43] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.98 Safari/537.36 T7/13.38 languageType/0 bdh_dvt/0 bdh_de/1 bdh_ds/1 bdhonorbrowser/******* (P1 13)
[2025-08-04 17:53:43] [INFO] 客户端IP: **************
[2025-08-04 17:53:43] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 17:53:43] [INFO] 页面类型: merchant
[2025-08-04 17:53:43] [INFO] 商户ID: '8034567958'
[2025-08-04 17:53:43] [INFO] 商品ID: ''
[2025-08-04 17:53:43] [INFO] 订单ID: ''
[2025-08-04 17:56:44] [INFO] === 新的访问开始 ===
[2025-08-04 17:56:44] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 17:56:44] [INFO] 请求方法: GET
[2025-08-04 17:56:44] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 17:56:44] [INFO] POST参数: []
[2025-08-04 17:56:44] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.98 Safari/537.36 T7/13.38 languageType/0 bdh_dvt/0 bdh_de/1 bdh_ds/1 bdhonorbrowser/******* (P1 13)
[2025-08-04 17:56:44] [INFO] 客户端IP: **************
[2025-08-04 17:56:44] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 17:56:44] [INFO] 页面类型: merchant
[2025-08-04 17:56:44] [INFO] 商户ID: '8034567958'
[2025-08-04 17:56:44] [INFO] 商品ID: ''
[2025-08-04 17:56:44] [INFO] 订单ID: ''
[2025-08-04 18:04:00] [INFO] === 新的访问开始 ===
[2025-08-04 18:04:00] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 18:04:00] [INFO] 请求方法: GET
[2025-08-04 18:04:00] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 18:04:00] [INFO] POST参数: []
[2025-08-04 18:04:00] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Mobile/21G93 Safari/604.1
[2025-08-04 18:04:00] [INFO] 客户端IP: *************
[2025-08-04 18:04:00] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 18:04:00] [INFO] 页面类型: merchant
[2025-08-04 18:04:00] [INFO] 商户ID: '8034567958'
[2025-08-04 18:04:00] [INFO] 商品ID: ''
[2025-08-04 18:04:00] [INFO] 订单ID: ''
[2025-08-04 18:08:08] [INFO] === 新的访问开始 ===
[2025-08-04 18:08:08] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 18:08:08] [INFO] 请求方法: GET
[2025-08-04 18:08:08] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 18:08:08] [INFO] POST参数: []
[2025-08-04 18:08:08] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 6.0.1; SOV33 Build/35.0.D.0.326) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.91 Mobile Safari/537.36
[2025-08-04 18:08:08] [INFO] 客户端IP: **************
[2025-08-04 18:08:08] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 18:08:08] [INFO] 页面类型: merchant
[2025-08-04 18:08:08] [INFO] 商户ID: '8034567958'
[2025-08-04 18:08:08] [INFO] 商品ID: ''
[2025-08-04 18:08:08] [INFO] 订单ID: ''
[2025-08-04 18:12:47] [INFO] === 新的访问开始 ===
[2025-08-04 18:12:47] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 18:12:47] [INFO] 请求方法: GET
[2025-08-04 18:12:47] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 18:12:47] [INFO] POST参数: []
[2025-08-04 18:12:47] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 12; zh-cn; Redmi K30 Build/SKQ1.210908.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/20.0.20728
[2025-08-04 18:12:47] [INFO] 客户端IP: **************
[2025-08-04 18:12:47] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 18:12:47] [INFO] 页面类型: merchant
[2025-08-04 18:12:47] [INFO] 商户ID: '8034567958'
[2025-08-04 18:12:47] [INFO] 商品ID: ''
[2025-08-04 18:12:47] [INFO] 订单ID: ''
[2025-08-04 18:50:42] [INFO] === 新的访问开始 ===
[2025-08-04 18:50:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 18:50:42] [INFO] 请求方法: GET
[2025-08-04 18:50:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 18:50:42] [INFO] POST参数: []
[2025-08-04 18:50:42] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.156 Mobile/15E148 Safari/604.1
[2025-08-04 18:50:42] [INFO] 客户端IP: **************
[2025-08-04 18:50:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 18:50:42] [INFO] 页面类型: merchant
[2025-08-04 18:50:42] [INFO] 商户ID: '8034567958'
[2025-08-04 18:50:42] [INFO] 商品ID: ''
[2025-08-04 18:50:42] [INFO] 订单ID: ''
[2025-08-04 18:55:00] [INFO] === 新的访问开始 ===
[2025-08-04 18:55:00] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 18:55:00] [INFO] 请求方法: GET
[2025-08-04 18:55:00] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 18:55:00] [INFO] POST参数: []
[2025-08-04 18:55:00] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; JNY-AL10 Build/HUAWEIJNY-AL10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Mobile Safari/537.36
[2025-08-04 18:55:00] [INFO] 客户端IP: **************
[2025-08-04 18:55:00] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 18:55:00] [INFO] 页面类型: merchant
[2025-08-04 18:55:00] [INFO] 商户ID: '8034567958'
[2025-08-04 18:55:00] [INFO] 商品ID: ''
[2025-08-04 18:55:00] [INFO] 订单ID: ''
[2025-08-04 19:04:48] [INFO] === 新的访问开始 ===
[2025-08-04 19:04:48] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 19:04:48] [INFO] 请求方法: GET
[2025-08-04 19:04:48] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 19:04:48] [INFO] POST参数: []
[2025-08-04 19:04:48] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 13; zh-cn; PHJ110 Build/TP1A.220905.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.61 Mobile Safari/537.36 HeyTapBrowser/*********
[2025-08-04 19:04:48] [INFO] 客户端IP: **************
[2025-08-04 19:04:48] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 19:04:48] [INFO] 页面类型: merchant
[2025-08-04 19:04:48] [INFO] 商户ID: '8034567958'
[2025-08-04 19:04:48] [INFO] 商品ID: ''
[2025-08-04 19:04:48] [INFO] 订单ID: ''
[2025-08-04 19:56:19] [INFO] === 新的访问开始 ===
[2025-08-04 19:56:19] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 19:56:19] [INFO] 请求方法: GET
[2025-08-04 19:56:19] [INFO] GET参数: {"sp":"23"}
[2025-08-04 19:56:19] [INFO] POST参数: []
[2025-08-04 19:56:19] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-04 19:56:19] [INFO] 客户端IP: **************
[2025-08-04 19:56:19] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 19:56:19] [INFO] 页面类型: product
[2025-08-04 19:56:19] [INFO] 商户ID: ''
[2025-08-04 19:56:19] [INFO] 商品ID: '23'
[2025-08-04 19:56:19] [INFO] 订单ID: ''
[2025-08-04 19:59:03] [INFO] === 新的访问开始 ===
[2025-08-04 19:59:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 19:59:03] [INFO] 请求方法: GET
[2025-08-04 19:59:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 19:59:03] [INFO] POST参数: []
[2025-08-04 19:59:03] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-04 19:59:03] [INFO] 客户端IP: *************
[2025-08-04 19:59:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 19:59:03] [INFO] 页面类型: merchant
[2025-08-04 19:59:03] [INFO] 商户ID: '8034567958'
[2025-08-04 19:59:03] [INFO] 商品ID: ''
[2025-08-04 19:59:03] [INFO] 订单ID: ''
[2025-08-04 19:59:31] [INFO] === 新的访问开始 ===
[2025-08-04 19:59:31] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 19:59:31] [INFO] 请求方法: GET
[2025-08-04 19:59:31] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 19:59:31] [INFO] POST参数: []
[2025-08-04 19:59:31] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 13; zh-cn; PHJ110 Build/TP1A.220905.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/90.0.4430.61 Mobile Safari/537.36 HeyTapBrowser/*********
[2025-08-04 19:59:31] [INFO] 客户端IP: **************
[2025-08-04 19:59:31] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 19:59:31] [INFO] 页面类型: merchant
[2025-08-04 19:59:31] [INFO] 商户ID: '8034567958'
[2025-08-04 19:59:31] [INFO] 商品ID: ''
[2025-08-04 19:59:31] [INFO] 订单ID: ''
[2025-08-04 20:46:58] [INFO] === 新的访问开始 ===
[2025-08-04 20:46:58] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 20:46:58] [INFO] 请求方法: GET
[2025-08-04 20:46:58] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 20:46:58] [INFO] POST参数: []
[2025-08-04 20:46:58] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-04 20:46:58] [INFO] 客户端IP: ************
[2025-08-04 20:46:58] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 20:46:58] [INFO] 页面类型: merchant
[2025-08-04 20:46:58] [INFO] 商户ID: '8034567958'
[2025-08-04 20:46:58] [INFO] 商品ID: ''
[2025-08-04 20:46:58] [INFO] 订单ID: ''
[2025-08-04 21:20:53] [INFO] === 新的访问开始 ===
[2025-08-04 21:20:53] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 21:20:53] [INFO] 请求方法: GET
[2025-08-04 21:20:53] [INFO] GET参数: {"sp":"23"}
[2025-08-04 21:20:53] [INFO] POST参数: []
[2025-08-04 21:20:53] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Mobile/21B101 Safari/604.1
[2025-08-04 21:20:53] [INFO] 客户端IP: **************
[2025-08-04 21:20:53] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 21:20:53] [INFO] 页面类型: product
[2025-08-04 21:20:53] [INFO] 商户ID: ''
[2025-08-04 21:20:53] [INFO] 商品ID: '23'
[2025-08-04 21:20:53] [INFO] 订单ID: ''
[2025-08-04 21:32:11] [INFO] === 新的访问开始 ===
[2025-08-04 21:32:11] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 21:32:11] [INFO] 请求方法: GET
[2025-08-04 21:32:11] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 21:32:11] [INFO] POST参数: []
[2025-08-04 21:32:11] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Mobile/21B101 Safari/604.1
[2025-08-04 21:32:11] [INFO] 客户端IP: **************
[2025-08-04 21:32:11] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 21:32:11] [INFO] 页面类型: merchant
[2025-08-04 21:32:11] [INFO] 商户ID: '8034567958'
[2025-08-04 21:32:11] [INFO] 商品ID: ''
[2025-08-04 21:32:11] [INFO] 订单ID: ''
[2025-08-04 21:34:24] [INFO] === 新的访问开始 ===
[2025-08-04 21:34:24] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 21:34:24] [INFO] 请求方法: GET
[2025-08-04 21:34:24] [INFO] GET参数: {"sp":"23"}
[2025-08-04 21:34:24] [INFO] POST参数: []
[2025-08-04 21:34:24] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.2 Mobile/21B101 Safari/604.1
[2025-08-04 21:34:24] [INFO] 客户端IP: **************
[2025-08-04 21:34:24] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 21:34:24] [INFO] 页面类型: product
[2025-08-04 21:34:24] [INFO] 商户ID: ''
[2025-08-04 21:34:24] [INFO] 商品ID: '23'
[2025-08-04 21:34:24] [INFO] 订单ID: ''
[2025-08-04 21:40:01] [INFO] === 新的访问开始 ===
[2025-08-04 21:40:01] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 21:40:01] [INFO] 请求方法: GET
[2025-08-04 21:40:01] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 21:40:01] [INFO] POST参数: []
[2025-08-04 21:40:01] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/20B82 Safari/604.1
[2025-08-04 21:40:01] [INFO] 客户端IP: ************
[2025-08-04 21:40:01] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 21:40:01] [INFO] 页面类型: merchant
[2025-08-04 21:40:01] [INFO] 商户ID: '8034567958'
[2025-08-04 21:40:01] [INFO] 商品ID: ''
[2025-08-04 21:40:01] [INFO] 订单ID: ''
[2025-08-04 22:06:45] [INFO] === 新的访问开始 ===
[2025-08-04 22:06:45] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:06:45] [INFO] 请求方法: GET
[2025-08-04 22:06:45] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:06:45] [INFO] POST参数: []
[2025-08-04 22:06:45] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:06:45] [INFO] 客户端IP: **************
[2025-08-04 22:06:45] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:06:45] [INFO] 页面类型: product
[2025-08-04 22:06:45] [INFO] 商户ID: ''
[2025-08-04 22:06:45] [INFO] 商品ID: '23'
[2025-08-04 22:06:45] [INFO] 订单ID: ''
[2025-08-04 22:07:16] [INFO] === 新的访问开始 ===
[2025-08-04 22:07:16] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:07:16] [INFO] 请求方法: GET
[2025-08-04 22:07:16] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:07:16] [INFO] POST参数: []
[2025-08-04 22:07:16] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:07:16] [INFO] 客户端IP: **************
[2025-08-04 22:07:16] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:07:16] [INFO] 页面类型: product
[2025-08-04 22:07:16] [INFO] 商户ID: ''
[2025-08-04 22:07:16] [INFO] 商品ID: '23'
[2025-08-04 22:07:16] [INFO] 订单ID: ''
[2025-08-04 22:07:24] [INFO] === 新的访问开始 ===
[2025-08-04 22:07:24] [INFO] 请求URI: /shop.php?dd=7696475097
[2025-08-04 22:07:24] [INFO] 请求方法: GET
[2025-08-04 22:07:24] [INFO] GET参数: {"dd":"7696475097"}
[2025-08-04 22:07:24] [INFO] POST参数: []
[2025-08-04 22:07:24] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:07:24] [INFO] 客户端IP: **************
[2025-08-04 22:07:24] [INFO] 解析参数 - sj: '', sp: '', dd: '7696475097'
[2025-08-04 22:07:24] [INFO] 页面类型: order
[2025-08-04 22:07:24] [INFO] 商户ID: ''
[2025-08-04 22:07:24] [INFO] 商品ID: ''
[2025-08-04 22:07:24] [INFO] 订单ID: '7696475097'
[2025-08-04 22:07:47] [INFO] === 新的访问开始 ===
[2025-08-04 22:07:47] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:07:47] [INFO] 请求方法: GET
[2025-08-04 22:07:47] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:07:47] [INFO] POST参数: []
[2025-08-04 22:07:47] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:07:47] [INFO] 客户端IP: **************
[2025-08-04 22:07:47] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:07:47] [INFO] 页面类型: product
[2025-08-04 22:07:47] [INFO] 商户ID: ''
[2025-08-04 22:07:47] [INFO] 商品ID: '23'
[2025-08-04 22:07:47] [INFO] 订单ID: ''
[2025-08-04 22:08:10] [INFO] === 新的访问开始 ===
[2025-08-04 22:08:10] [INFO] 请求URI: /shop.php?dd=17542311704%E2%9C%85%20%E6%94%AF%E4%BB%98%E6%88%90%E5%8A%9F%EF%BC%81%20%20%F0%9F%93%8B%20%E8%AE%A2%E5%8D%95%E5%8F%B7%EF%BC%9AORDER17542311704311
[2025-08-04 22:08:10] [INFO] 请求方法: GET
[2025-08-04 22:08:10] [INFO] GET参数: {"dd":"17542311704\u2705 \u652f\u4ed8\u6210\u529f\uff01  \ud83d\udccb \u8ba2\u5355\u53f7\uff1aORDER17542311704311"}
[2025-08-04 22:08:10] [INFO] POST参数: []
[2025-08-04 22:08:10] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:08:10] [INFO] 客户端IP: **************
[2025-08-04 22:08:10] [INFO] 解析参数 - sj: '', sp: '', dd: '17542311704✅ 支付成功！  📋 订单号：ORDER17542311704311'
[2025-08-04 22:08:10] [INFO] 页面类型: order
[2025-08-04 22:08:10] [INFO] 商户ID: ''
[2025-08-04 22:08:10] [INFO] 商品ID: ''
[2025-08-04 22:08:10] [INFO] 订单ID: '17542311704✅ 支付成功！  📋 订单号：ORDER17542311704311'
[2025-08-04 22:08:19] [INFO] === 新的访问开始 ===
[2025-08-04 22:08:19] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:08:19] [INFO] 请求方法: GET
[2025-08-04 22:08:19] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:08:19] [INFO] POST参数: []
[2025-08-04 22:08:19] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:08:19] [INFO] 客户端IP: **************
[2025-08-04 22:08:19] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:08:19] [INFO] 页面类型: product
[2025-08-04 22:08:19] [INFO] 商户ID: ''
[2025-08-04 22:08:19] [INFO] 商品ID: '23'
[2025-08-04 22:08:19] [INFO] 订单ID: ''
[2025-08-04 22:08:41] [INFO] === 新的访问开始 ===
[2025-08-04 22:08:41] [INFO] 请求URI: /shop.php?dd=ORDER17542311704311
[2025-08-04 22:08:41] [INFO] 请求方法: GET
[2025-08-04 22:08:41] [INFO] GET参数: {"dd":"ORDER17542311704311"}
[2025-08-04 22:08:41] [INFO] POST参数: []
[2025-08-04 22:08:41] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:08:41] [INFO] 客户端IP: **************
[2025-08-04 22:08:41] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17542311704311'
[2025-08-04 22:08:41] [INFO] 页面类型: order
[2025-08-04 22:08:41] [INFO] 商户ID: ''
[2025-08-04 22:08:41] [INFO] 商品ID: ''
[2025-08-04 22:08:41] [INFO] 订单ID: 'ORDER17542311704311'
[2025-08-04 22:11:36] [INFO] === 新的访问开始 ===
[2025-08-04 22:11:36] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:11:36] [INFO] 请求方法: GET
[2025-08-04 22:11:36] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:11:36] [INFO] POST参数: []
[2025-08-04 22:11:36] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-04 22:11:36] [INFO] 客户端IP: **************
[2025-08-04 22:11:36] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:11:36] [INFO] 页面类型: product
[2025-08-04 22:11:36] [INFO] 商户ID: ''
[2025-08-04 22:11:36] [INFO] 商品ID: '23'
[2025-08-04 22:11:36] [INFO] 订单ID: ''
[2025-08-04 22:35:11] [INFO] === 新的访问开始 ===
[2025-08-04 22:35:11] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:35:11] [INFO] 请求方法: GET
[2025-08-04 22:35:11] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:35:11] [INFO] POST参数: []
[2025-08-04 22:35:11] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 13; zh-cn; RMX3031 Build/TP1A.220905.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/*********
[2025-08-04 22:35:11] [INFO] 客户端IP: *************
[2025-08-04 22:35:11] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:35:11] [INFO] 页面类型: product
[2025-08-04 22:35:11] [INFO] 商户ID: ''
[2025-08-04 22:35:11] [INFO] 商品ID: '23'
[2025-08-04 22:35:11] [INFO] 订单ID: ''
[2025-08-04 22:38:01] [INFO] === 新的访问开始 ===
[2025-08-04 22:38:01] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:38:01] [INFO] 请求方法: GET
[2025-08-04 22:38:01] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:38:01] [INFO] POST参数: []
[2025-08-04 22:38:01] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-04 22:38:01] [INFO] 客户端IP: *************
[2025-08-04 22:38:01] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:38:01] [INFO] 页面类型: product
[2025-08-04 22:38:01] [INFO] 商户ID: ''
[2025-08-04 22:38:01] [INFO] 商品ID: '23'
[2025-08-04 22:38:01] [INFO] 订单ID: ''
[2025-08-04 22:38:56] [INFO] === 新的访问开始 ===
[2025-08-04 22:38:56] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 22:38:56] [INFO] 请求方法: GET
[2025-08-04 22:38:56] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 22:38:56] [INFO] POST参数: []
[2025-08-04 22:38:56] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 11; NX629J Build/RKQ1.200826.002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.120 Mobile Safari/537.36
[2025-08-04 22:38:56] [INFO] 客户端IP: **************
[2025-08-04 22:38:56] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 22:38:56] [INFO] 页面类型: merchant
[2025-08-04 22:38:56] [INFO] 商户ID: '8034567958'
[2025-08-04 22:38:56] [INFO] 商品ID: ''
[2025-08-04 22:38:56] [INFO] 订单ID: ''
[2025-08-04 22:39:01] [INFO] === 新的访问开始 ===
[2025-08-04 22:39:01] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:39:01] [INFO] 请求方法: GET
[2025-08-04 22:39:01] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:39:01] [INFO] POST参数: []
[2025-08-04 22:39:01] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 6.0.1; SOV33 Build/35.0.D.0.326) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.91 Mobile Safari/537.36
[2025-08-04 22:39:01] [INFO] 客户端IP: ************
[2025-08-04 22:39:01] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:39:01] [INFO] 页面类型: product
[2025-08-04 22:39:01] [INFO] 商户ID: ''
[2025-08-04 22:39:01] [INFO] 商品ID: '23'
[2025-08-04 22:39:01] [INFO] 订单ID: ''
[2025-08-04 22:39:23] [INFO] === 新的访问开始 ===
[2025-08-04 22:39:23] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 22:39:23] [INFO] 请求方法: GET
[2025-08-04 22:39:23] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 22:39:23] [INFO] POST参数: []
[2025-08-04 22:39:23] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 11; NX629J Build/RKQ1.200826.002) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.120 Mobile Safari/537.36
[2025-08-04 22:39:23] [INFO] 客户端IP: **************
[2025-08-04 22:39:23] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 22:39:23] [INFO] 页面类型: merchant
[2025-08-04 22:39:23] [INFO] 商户ID: '8034567958'
[2025-08-04 22:39:23] [INFO] 商品ID: ''
[2025-08-04 22:39:23] [INFO] 订单ID: ''
[2025-08-04 22:40:59] [INFO] === 新的访问开始 ===
[2025-08-04 22:40:59] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:40:59] [INFO] 请求方法: GET
[2025-08-04 22:40:59] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:40:59] [INFO] POST参数: []
[2025-08-04 22:40:59] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-04 22:40:59] [INFO] 客户端IP: *************
[2025-08-04 22:40:59] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:40:59] [INFO] 页面类型: product
[2025-08-04 22:40:59] [INFO] 商户ID: ''
[2025-08-04 22:40:59] [INFO] 商品ID: '23'
[2025-08-04 22:40:59] [INFO] 订单ID: ''
[2025-08-04 22:43:37] [INFO] === 新的访问开始 ===
[2025-08-04 22:43:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 22:43:37] [INFO] 请求方法: GET
[2025-08-04 22:43:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 22:43:37] [INFO] POST参数: []
[2025-08-04 22:43:37] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Mobile/21D61 Safari/604.1
[2025-08-04 22:43:37] [INFO] 客户端IP: ************
[2025-08-04 22:43:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 22:43:37] [INFO] 页面类型: merchant
[2025-08-04 22:43:37] [INFO] 商户ID: '8034567958'
[2025-08-04 22:43:37] [INFO] 商品ID: ''
[2025-08-04 22:43:37] [INFO] 订单ID: ''
[2025-08-04 22:43:58] [INFO] === 新的访问开始 ===
[2025-08-04 22:43:58] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:43:58] [INFO] 请求方法: GET
[2025-08-04 22:43:58] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:43:58] [INFO] POST参数: []
[2025-08-04 22:43:58] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-04 22:43:58] [INFO] 客户端IP: *************
[2025-08-04 22:43:58] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:43:58] [INFO] 页面类型: product
[2025-08-04 22:43:58] [INFO] 商户ID: ''
[2025-08-04 22:43:58] [INFO] 商品ID: '23'
[2025-08-04 22:43:58] [INFO] 订单ID: ''
[2025-08-04 22:44:06] [INFO] === 新的访问开始 ===
[2025-08-04 22:44:06] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:44:06] [INFO] 请求方法: GET
[2025-08-04 22:44:06] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:44:06] [INFO] POST参数: []
[2025-08-04 22:44:06] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/22E240 Safari/604.1
[2025-08-04 22:44:06] [INFO] 客户端IP: **************
[2025-08-04 22:44:06] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:44:06] [INFO] 页面类型: product
[2025-08-04 22:44:06] [INFO] 商户ID: ''
[2025-08-04 22:44:06] [INFO] 商品ID: '23'
[2025-08-04 22:44:06] [INFO] 订单ID: ''
[2025-08-04 22:46:38] [INFO] === 新的访问开始 ===
[2025-08-04 22:46:38] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 22:46:38] [INFO] 请求方法: GET
[2025-08-04 22:46:38] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 22:46:38] [INFO] POST参数: []
[2025-08-04 22:46:38] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-04 22:46:38] [INFO] 客户端IP: *************
[2025-08-04 22:46:38] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 22:46:38] [INFO] 页面类型: merchant
[2025-08-04 22:46:38] [INFO] 商户ID: '8034567958'
[2025-08-04 22:46:38] [INFO] 商品ID: ''
[2025-08-04 22:46:38] [INFO] 订单ID: ''
[2025-08-04 22:47:04] [INFO] === 新的访问开始 ===
[2025-08-04 22:47:04] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 22:47:04] [INFO] 请求方法: GET
[2025-08-04 22:47:04] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 22:47:04] [INFO] POST参数: []
[2025-08-04 22:47:04] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-04 22:47:04] [INFO] 客户端IP: *************
[2025-08-04 22:47:04] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 22:47:04] [INFO] 页面类型: merchant
[2025-08-04 22:47:04] [INFO] 商户ID: '8034567958'
[2025-08-04 22:47:04] [INFO] 商品ID: ''
[2025-08-04 22:47:04] [INFO] 订单ID: ''
[2025-08-04 22:47:12] [INFO] === 新的访问开始 ===
[2025-08-04 22:47:12] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:47:12] [INFO] 请求方法: GET
[2025-08-04 22:47:12] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:47:12] [INFO] POST参数: []
[2025-08-04 22:47:12] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-04 22:47:12] [INFO] 客户端IP: *************
[2025-08-04 22:47:12] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:47:12] [INFO] 页面类型: product
[2025-08-04 22:47:12] [INFO] 商户ID: ''
[2025-08-04 22:47:12] [INFO] 商品ID: '23'
[2025-08-04 22:47:12] [INFO] 订单ID: ''
[2025-08-04 22:48:46] [INFO] === 新的访问开始 ===
[2025-08-04 22:48:46] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 22:48:46] [INFO] 请求方法: GET
[2025-08-04 22:48:46] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 22:48:46] [INFO] POST参数: []
[2025-08-04 22:48:46] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-04 22:48:46] [INFO] 客户端IP: *************
[2025-08-04 22:48:46] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 22:48:46] [INFO] 页面类型: merchant
[2025-08-04 22:48:46] [INFO] 商户ID: '8034567958'
[2025-08-04 22:48:46] [INFO] 商品ID: ''
[2025-08-04 22:48:46] [INFO] 订单ID: ''
[2025-08-04 22:57:18] [INFO] === 新的访问开始 ===
[2025-08-04 22:57:18] [INFO] 请求URI: /shop.php?sp=23
[2025-08-04 22:57:18] [INFO] 请求方法: GET
[2025-08-04 22:57:18] [INFO] GET参数: {"sp":"23"}
[2025-08-04 22:57:18] [INFO] POST参数: []
[2025-08-04 22:57:18] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 22:57:18] [INFO] 客户端IP: **************
[2025-08-04 22:57:18] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-04 22:57:18] [INFO] 页面类型: product
[2025-08-04 22:57:18] [INFO] 商户ID: ''
[2025-08-04 22:57:18] [INFO] 商品ID: '23'
[2025-08-04 22:57:18] [INFO] 订单ID: ''
[2025-08-04 22:57:24] [INFO] === 新的访问开始 ===
[2025-08-04 22:57:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 22:57:24] [INFO] 请求方法: GET
[2025-08-04 22:57:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 22:57:24] [INFO] POST参数: []
[2025-08-04 22:57:24] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 22:57:24] [INFO] 客户端IP: **************
[2025-08-04 22:57:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 22:57:24] [INFO] 页面类型: merchant
[2025-08-04 22:57:24] [INFO] 商户ID: '8034567958'
[2025-08-04 22:57:24] [INFO] 商品ID: ''
[2025-08-04 22:57:24] [INFO] 订单ID: ''
[2025-08-04 23:08:15] [INFO] === 新的访问开始 ===
[2025-08-04 23:08:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 23:08:15] [INFO] 请求方法: GET
[2025-08-04 23:08:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 23:08:15] [INFO] POST参数: []
[2025-08-04 23:08:15] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-04 23:08:15] [INFO] 客户端IP: **************
[2025-08-04 23:08:15] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 23:08:15] [INFO] 页面类型: merchant
[2025-08-04 23:08:15] [INFO] 商户ID: '8034567958'
[2025-08-04 23:08:15] [INFO] 商品ID: ''
[2025-08-04 23:08:15] [INFO] 订单ID: ''
[2025-08-04 23:09:42] [INFO] === 新的访问开始 ===
[2025-08-04 23:09:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 23:09:42] [INFO] 请求方法: GET
[2025-08-04 23:09:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 23:09:42] [INFO] POST参数: []
[2025-08-04 23:09:42] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-04 23:09:42] [INFO] 客户端IP: **************
[2025-08-04 23:09:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 23:09:42] [INFO] 页面类型: merchant
[2025-08-04 23:09:42] [INFO] 商户ID: '8034567958'
[2025-08-04 23:09:42] [INFO] 商品ID: ''
[2025-08-04 23:09:42] [INFO] 订单ID: ''
[2025-08-04 23:10:55] [INFO] === 新的访问开始 ===
[2025-08-04 23:10:55] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 23:10:55] [INFO] 请求方法: GET
[2025-08-04 23:10:55] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 23:10:55] [INFO] POST参数: []
[2025-08-04 23:10:55] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-04 23:10:55] [INFO] 客户端IP: **************
[2025-08-04 23:10:55] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 23:10:55] [INFO] 页面类型: merchant
[2025-08-04 23:10:55] [INFO] 商户ID: '8034567958'
[2025-08-04 23:10:55] [INFO] 商品ID: ''
[2025-08-04 23:10:55] [INFO] 订单ID: ''
[2025-08-04 23:15:00] [INFO] === 新的访问开始 ===
[2025-08-04 23:15:00] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 23:15:00] [INFO] 请求方法: GET
[2025-08-04 23:15:00] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 23:15:00] [INFO] POST参数: []
[2025-08-04 23:15:00] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-04 23:15:00] [INFO] 客户端IP: **************
[2025-08-04 23:15:00] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 23:15:00] [INFO] 页面类型: merchant
[2025-08-04 23:15:00] [INFO] 商户ID: '8034567958'
[2025-08-04 23:15:00] [INFO] 商品ID: ''
[2025-08-04 23:15:00] [INFO] 订单ID: ''
[2025-08-04 23:26:30] [INFO] === 新的访问开始 ===
[2025-08-04 23:26:30] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 23:26:30] [INFO] 请求方法: GET
[2025-08-04 23:26:30] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 23:26:30] [INFO] POST参数: []
[2025-08-04 23:26:30] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:26:30] [INFO] 客户端IP: **************
[2025-08-04 23:26:30] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 23:26:30] [INFO] 页面类型: merchant
[2025-08-04 23:26:30] [INFO] 商户ID: '8034567958'
[2025-08-04 23:26:30] [INFO] 商品ID: ''
[2025-08-04 23:26:30] [INFO] 订单ID: ''
[2025-08-04 23:26:32] [INFO] === 新的访问开始 ===
[2025-08-04 23:26:32] [INFO] 请求URI: /shop.php?sj=7500099799%E3%80%81
[2025-08-04 23:26:32] [INFO] 请求方法: GET
[2025-08-04 23:26:32] [INFO] GET参数: {"sj":"7500099799\u3001"}
[2025-08-04 23:26:32] [INFO] POST参数: []
[2025-08-04 23:26:32] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:26:32] [INFO] 客户端IP: **************
[2025-08-04 23:26:32] [INFO] 解析参数 - sj: '7500099799、', sp: '', dd: ''
[2025-08-04 23:26:32] [INFO] 页面类型: merchant
[2025-08-04 23:26:32] [INFO] 商户ID: '7500099799、'
[2025-08-04 23:26:32] [INFO] 商品ID: ''
[2025-08-04 23:26:32] [INFO] 订单ID: ''
[2025-08-04 23:26:36] [INFO] === 新的访问开始 ===
[2025-08-04 23:26:36] [INFO] 请求URI: /shop.php?sj=7500099799
[2025-08-04 23:26:36] [INFO] 请求方法: GET
[2025-08-04 23:26:36] [INFO] GET参数: {"sj":"7500099799"}
[2025-08-04 23:26:36] [INFO] POST参数: []
[2025-08-04 23:26:36] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:26:36] [INFO] 客户端IP: **************
[2025-08-04 23:26:36] [INFO] 解析参数 - sj: '7500099799', sp: '', dd: ''
[2025-08-04 23:26:36] [INFO] 页面类型: merchant
[2025-08-04 23:26:36] [INFO] 商户ID: '7500099799'
[2025-08-04 23:26:36] [INFO] 商品ID: ''
[2025-08-04 23:26:36] [INFO] 订单ID: ''
[2025-08-04 23:26:56] [INFO] === 新的访问开始 ===
[2025-08-04 23:26:56] [INFO] 请求URI: /shop.php?sj=7501547838
[2025-08-04 23:26:56] [INFO] 请求方法: GET
[2025-08-04 23:26:56] [INFO] GET参数: {"sj":"7501547838"}
[2025-08-04 23:26:56] [INFO] POST参数: []
[2025-08-04 23:26:56] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:26:56] [INFO] 客户端IP: **************
[2025-08-04 23:26:56] [INFO] 解析参数 - sj: '7501547838', sp: '', dd: ''
[2025-08-04 23:26:56] [INFO] 页面类型: merchant
[2025-08-04 23:26:56] [INFO] 商户ID: '7501547838'
[2025-08-04 23:26:56] [INFO] 商品ID: ''
[2025-08-04 23:26:56] [INFO] 订单ID: ''
[2025-08-04 23:27:17] [INFO] === 新的访问开始 ===
[2025-08-04 23:27:17] [INFO] 请求URI: /shop.php?sj=8127402430
[2025-08-04 23:27:17] [INFO] 请求方法: GET
[2025-08-04 23:27:17] [INFO] GET参数: {"sj":"8127402430"}
[2025-08-04 23:27:17] [INFO] POST参数: []
[2025-08-04 23:27:17] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:27:17] [INFO] 客户端IP: **************
[2025-08-04 23:27:17] [INFO] 解析参数 - sj: '8127402430', sp: '', dd: ''
[2025-08-04 23:27:17] [INFO] 页面类型: merchant
[2025-08-04 23:27:17] [INFO] 商户ID: '8127402430'
[2025-08-04 23:27:17] [INFO] 商品ID: ''
[2025-08-04 23:27:17] [INFO] 订单ID: ''
[2025-08-04 23:28:05] [INFO] === 新的访问开始 ===
[2025-08-04 23:28:05] [INFO] 请求URI: /shop.php?sj=6192491382
[2025-08-04 23:28:05] [INFO] 请求方法: GET
[2025-08-04 23:28:05] [INFO] GET参数: {"sj":"6192491382"}
[2025-08-04 23:28:05] [INFO] POST参数: []
[2025-08-04 23:28:05] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:28:05] [INFO] 客户端IP: **************
[2025-08-04 23:28:05] [INFO] 解析参数 - sj: '6192491382', sp: '', dd: ''
[2025-08-04 23:28:05] [INFO] 页面类型: merchant
[2025-08-04 23:28:05] [INFO] 商户ID: '6192491382'
[2025-08-04 23:28:05] [INFO] 商品ID: ''
[2025-08-04 23:28:05] [INFO] 订单ID: ''
[2025-08-04 23:28:20] [INFO] === 新的访问开始 ===
[2025-08-04 23:28:20] [INFO] 请求URI: /shop.php?sj=6941597636
[2025-08-04 23:28:20] [INFO] 请求方法: GET
[2025-08-04 23:28:20] [INFO] GET参数: {"sj":"6941597636"}
[2025-08-04 23:28:20] [INFO] POST参数: []
[2025-08-04 23:28:20] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:28:20] [INFO] 客户端IP: **************
[2025-08-04 23:28:20] [INFO] 解析参数 - sj: '6941597636', sp: '', dd: ''
[2025-08-04 23:28:20] [INFO] 页面类型: merchant
[2025-08-04 23:28:20] [INFO] 商户ID: '6941597636'
[2025-08-04 23:28:20] [INFO] 商品ID: ''
[2025-08-04 23:28:20] [INFO] 订单ID: ''
[2025-08-04 23:28:34] [INFO] === 新的访问开始 ===
[2025-08-04 23:28:34] [INFO] 请求URI: /shop.php?sj=7500099799
[2025-08-04 23:28:34] [INFO] 请求方法: GET
[2025-08-04 23:28:34] [INFO] GET参数: {"sj":"7500099799"}
[2025-08-04 23:28:34] [INFO] POST参数: []
[2025-08-04 23:28:34] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 23:28:34] [INFO] 客户端IP: **************
[2025-08-04 23:28:34] [INFO] 解析参数 - sj: '7500099799', sp: '', dd: ''
[2025-08-04 23:28:34] [INFO] 页面类型: merchant
[2025-08-04 23:28:34] [INFO] 商户ID: '7500099799'
[2025-08-04 23:28:34] [INFO] 商品ID: ''
[2025-08-04 23:28:34] [INFO] 订单ID: ''
[2025-08-04 23:29:57] [INFO] === 新的访问开始 ===
[2025-08-04 23:29:57] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 23:29:57] [INFO] 请求方法: GET
[2025-08-04 23:29:57] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 23:29:57] [INFO] POST参数: []
[2025-08-04 23:29:57] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.64 Safari/537.36
[2025-08-04 23:29:57] [INFO] 客户端IP: **************
[2025-08-04 23:29:57] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 23:29:57] [INFO] 页面类型: merchant
[2025-08-04 23:29:57] [INFO] 商户ID: '8034567958'
[2025-08-04 23:29:57] [INFO] 商品ID: ''
[2025-08-04 23:29:57] [INFO] 订单ID: ''
[2025-08-04 23:39:49] [INFO] === 新的访问开始 ===
[2025-08-04 23:39:49] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 23:39:49] [INFO] 请求方法: GET
[2025-08-04 23:39:49] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 23:39:49] [INFO] POST参数: []
[2025-08-04 23:39:49] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.88 Mobile Safari/537.36
[2025-08-04 23:39:49] [INFO] 客户端IP: ************
[2025-08-04 23:39:49] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-04 23:39:49] [INFO] 页面类型: merchant
[2025-08-04 23:39:49] [INFO] 商户ID: '8034567958'
[2025-08-04 23:39:49] [INFO] 商品ID: ''
[2025-08-04 23:39:49] [INFO] 订单ID: ''
[2025-08-05 00:10:37] [INFO] === 新的访问开始 ===
[2025-08-05 00:10:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 00:10:37] [INFO] 请求方法: GET
[2025-08-05 00:10:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 00:10:37] [INFO] POST参数: []
[2025-08-05 00:10:37] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; V2353A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-05 00:10:37] [INFO] 客户端IP: **************
[2025-08-05 00:10:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 00:10:37] [INFO] 页面类型: merchant
[2025-08-05 00:10:37] [INFO] 商户ID: '8034567958'
[2025-08-05 00:10:37] [INFO] 商品ID: ''
[2025-08-05 00:10:37] [INFO] 订单ID: ''
[2025-08-05 00:10:38] [INFO] === 新的访问开始 ===
[2025-08-05 00:10:38] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 00:10:38] [INFO] 请求方法: GET
[2025-08-05 00:10:38] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 00:10:38] [INFO] POST参数: []
[2025-08-05 00:10:38] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-05 00:10:38] [INFO] 客户端IP: **************
[2025-08-05 00:10:38] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 00:10:38] [INFO] 页面类型: merchant
[2025-08-05 00:10:38] [INFO] 商户ID: '8034567958'
[2025-08-05 00:10:38] [INFO] 商品ID: ''
[2025-08-05 00:10:38] [INFO] 订单ID: ''
[2025-08-05 00:25:11] [INFO] === 新的访问开始 ===
[2025-08-05 00:25:11] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 00:25:11] [INFO] 请求方法: GET
[2025-08-05 00:25:11] [INFO] GET参数: {"sp":"23"}
[2025-08-05 00:25:11] [INFO] POST参数: []
[2025-08-05 00:25:11] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-05 00:25:11] [INFO] 客户端IP: **************
[2025-08-05 00:25:11] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 00:25:11] [INFO] 页面类型: product
[2025-08-05 00:25:11] [INFO] 商户ID: ''
[2025-08-05 00:25:11] [INFO] 商品ID: '23'
[2025-08-05 00:25:11] [INFO] 订单ID: ''
[2025-08-05 01:41:38] [INFO] === 新的访问开始 ===
[2025-08-05 01:41:38] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 01:41:38] [INFO] 请求方法: GET
[2025-08-05 01:41:38] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 01:41:38] [INFO] POST参数: []
[2025-08-05 01:41:38] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1
[2025-08-05 01:41:38] [INFO] 客户端IP: **************
[2025-08-05 01:41:38] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 01:41:38] [INFO] 页面类型: merchant
[2025-08-05 01:41:38] [INFO] 商户ID: '8034567958'
[2025-08-05 01:41:38] [INFO] 商品ID: ''
[2025-08-05 01:41:38] [INFO] 订单ID: ''
[2025-08-05 03:36:07] [INFO] === 新的访问开始 ===
[2025-08-05 03:36:07] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 03:36:07] [INFO] 请求方法: GET
[2025-08-05 03:36:07] [INFO] GET参数: {"sp":"23"}
[2025-08-05 03:36:07] [INFO] POST参数: []
[2025-08-05 03:36:07] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-05 03:36:07] [INFO] 客户端IP: **************
[2025-08-05 03:36:07] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 03:36:07] [INFO] 页面类型: product
[2025-08-05 03:36:07] [INFO] 商户ID: ''
[2025-08-05 03:36:07] [INFO] 商品ID: '23'
[2025-08-05 03:36:07] [INFO] 订单ID: ''
[2025-08-05 08:04:24] [INFO] === 新的访问开始 ===
[2025-08-05 08:04:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 08:04:24] [INFO] 请求方法: GET
[2025-08-05 08:04:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 08:04:24] [INFO] POST参数: []
[2025-08-05 08:04:24] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 12; zh-cn; ELS-AN00 Build/HUAWEIELS-AN00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.71 MQQBrowser/19.2 Mobile Safari/537.36 COVC/048201
[2025-08-05 08:04:24] [INFO] 客户端IP: **************
[2025-08-05 08:04:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 08:04:24] [INFO] 页面类型: merchant
[2025-08-05 08:04:24] [INFO] 商户ID: '8034567958'
[2025-08-05 08:04:24] [INFO] 商品ID: ''
[2025-08-05 08:04:24] [INFO] 订单ID: ''
[2025-08-05 08:05:37] [INFO] === 新的访问开始 ===
[2025-08-05 08:05:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 08:05:37] [INFO] 请求方法: GET
[2025-08-05 08:05:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 08:05:37] [INFO] POST参数: []
[2025-08-05 08:05:37] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 12; zh-cn; ELS-AN00 Build/HUAWEIELS-AN00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.71 MQQBrowser/19.2 Mobile Safari/537.36 COVC/048201
[2025-08-05 08:05:37] [INFO] 客户端IP: **************
[2025-08-05 08:05:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 08:05:37] [INFO] 页面类型: merchant
[2025-08-05 08:05:37] [INFO] 商户ID: '8034567958'
[2025-08-05 08:05:37] [INFO] 商品ID: ''
[2025-08-05 08:05:37] [INFO] 订单ID: ''
[2025-08-05 08:08:17] [INFO] === 新的访问开始 ===
[2025-08-05 08:08:17] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:08:17] [INFO] 请求方法: GET
[2025-08-05 08:08:17] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:08:17] [INFO] POST参数: []
[2025-08-05 08:08:17] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:08:17] [INFO] 客户端IP: **************
[2025-08-05 08:08:17] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:08:17] [INFO] 页面类型: product
[2025-08-05 08:08:17] [INFO] 商户ID: ''
[2025-08-05 08:08:17] [INFO] 商品ID: '23'
[2025-08-05 08:08:17] [INFO] 订单ID: ''
[2025-08-05 08:08:19] [INFO] === 新的访问开始 ===
[2025-08-05 08:08:19] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:08:19] [INFO] 请求方法: GET
[2025-08-05 08:08:19] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:08:19] [INFO] POST参数: []
[2025-08-05 08:08:19] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:08:19] [INFO] 客户端IP: **************
[2025-08-05 08:08:19] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:08:19] [INFO] 页面类型: product
[2025-08-05 08:08:19] [INFO] 商户ID: ''
[2025-08-05 08:08:19] [INFO] 商品ID: '23'
[2025-08-05 08:08:19] [INFO] 订单ID: ''
[2025-08-05 08:08:45] [INFO] === 新的访问开始 ===
[2025-08-05 08:08:45] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:08:45] [INFO] 请求方法: GET
[2025-08-05 08:08:45] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:08:45] [INFO] POST参数: []
[2025-08-05 08:08:45] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:08:45] [INFO] 客户端IP: **************
[2025-08-05 08:08:45] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:08:45] [INFO] 页面类型: product
[2025-08-05 08:08:45] [INFO] 商户ID: ''
[2025-08-05 08:08:45] [INFO] 商品ID: '23'
[2025-08-05 08:08:45] [INFO] 订单ID: ''
[2025-08-05 08:15:19] [INFO] === 新的访问开始 ===
[2025-08-05 08:15:19] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:15:19] [INFO] 请求方法: GET
[2025-08-05 08:15:19] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:15:19] [INFO] POST参数: []
[2025-08-05 08:15:19] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:15:19] [INFO] 客户端IP: **************
[2025-08-05 08:15:19] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:15:19] [INFO] 页面类型: product
[2025-08-05 08:15:19] [INFO] 商户ID: ''
[2025-08-05 08:15:19] [INFO] 商品ID: '23'
[2025-08-05 08:15:19] [INFO] 订单ID: ''
[2025-08-05 08:17:06] [INFO] === 新的访问开始 ===
[2025-08-05 08:17:06] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:17:06] [INFO] 请求方法: GET
[2025-08-05 08:17:06] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:17:06] [INFO] POST参数: []
[2025-08-05 08:17:06] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:17:06] [INFO] 客户端IP: **************
[2025-08-05 08:17:06] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:17:06] [INFO] 页面类型: product
[2025-08-05 08:17:06] [INFO] 商户ID: ''
[2025-08-05 08:17:06] [INFO] 商品ID: '23'
[2025-08-05 08:17:06] [INFO] 订单ID: ''
[2025-08-05 08:31:12] [INFO] === 新的访问开始 ===
[2025-08-05 08:31:12] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 08:31:12] [INFO] 请求方法: GET
[2025-08-05 08:31:12] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 08:31:12] [INFO] POST参数: []
[2025-08-05 08:31:12] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 12; zh-cn; ELS-AN00 Build/HUAWEIELS-AN00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.71 MQQBrowser/19.2 Mobile Safari/537.36 COVC/048201
[2025-08-05 08:31:12] [INFO] 客户端IP: *************
[2025-08-05 08:31:12] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 08:31:12] [INFO] 页面类型: merchant
[2025-08-05 08:31:12] [INFO] 商户ID: '8034567958'
[2025-08-05 08:31:12] [INFO] 商品ID: ''
[2025-08-05 08:31:12] [INFO] 订单ID: ''
[2025-08-05 08:35:06] [INFO] === 新的访问开始 ===
[2025-08-05 08:35:06] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:35:06] [INFO] 请求方法: GET
[2025-08-05 08:35:06] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:35:06] [INFO] POST参数: []
[2025-08-05 08:35:06] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:35:06] [INFO] 客户端IP: ************
[2025-08-05 08:35:06] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:35:06] [INFO] 页面类型: product
[2025-08-05 08:35:06] [INFO] 商户ID: ''
[2025-08-05 08:35:06] [INFO] 商品ID: '23'
[2025-08-05 08:35:06] [INFO] 订单ID: ''
[2025-08-05 08:36:33] [INFO] === 新的访问开始 ===
[2025-08-05 08:36:33] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:36:33] [INFO] 请求方法: GET
[2025-08-05 08:36:33] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:36:33] [INFO] POST参数: []
[2025-08-05 08:36:33] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:36:33] [INFO] 客户端IP: ************
[2025-08-05 08:36:33] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:36:33] [INFO] 页面类型: product
[2025-08-05 08:36:33] [INFO] 商户ID: ''
[2025-08-05 08:36:33] [INFO] 商品ID: '23'
[2025-08-05 08:36:33] [INFO] 订单ID: ''
[2025-08-05 08:37:17] [INFO] === 新的访问开始 ===
[2025-08-05 08:37:17] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 08:37:17] [INFO] 请求方法: GET
[2025-08-05 08:37:17] [INFO] GET参数: {"sp":"23"}
[2025-08-05 08:37:17] [INFO] POST参数: []
[2025-08-05 08:37:17] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; ELS-AN00; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.6.310 Mobile Safari/537.36
[2025-08-05 08:37:17] [INFO] 客户端IP: ************
[2025-08-05 08:37:17] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 08:37:17] [INFO] 页面类型: product
[2025-08-05 08:37:17] [INFO] 商户ID: ''
[2025-08-05 08:37:17] [INFO] 商品ID: '23'
[2025-08-05 08:37:17] [INFO] 订单ID: ''
[2025-08-05 08:38:45] [INFO] === 新的访问开始 ===
[2025-08-05 08:38:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 08:38:45] [INFO] 请求方法: GET
[2025-08-05 08:38:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 08:38:45] [INFO] POST参数: []
[2025-08-05 08:38:45] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 12; zh-cn; ELS-AN00 Build/HUAWEIELS-AN00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.71 MQQBrowser/19.2 Mobile Safari/537.36 COVC/048201
[2025-08-05 08:38:45] [INFO] 客户端IP: ************
[2025-08-05 08:38:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 08:38:45] [INFO] 页面类型: merchant
[2025-08-05 08:38:45] [INFO] 商户ID: '8034567958'
[2025-08-05 08:38:45] [INFO] 商品ID: ''
[2025-08-05 08:38:45] [INFO] 订单ID: ''
[2025-08-05 09:59:15] [INFO] === 新的访问开始 ===
[2025-08-05 09:59:15] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 09:59:15] [INFO] 请求方法: GET
[2025-08-05 09:59:15] [INFO] GET参数: {"sp":"23"}
[2025-08-05 09:59:15] [INFO] POST参数: []
[2025-08-05 09:59:15] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Mobile/21G93 Safari/604.1
[2025-08-05 09:59:15] [INFO] 客户端IP: **************
[2025-08-05 09:59:15] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 09:59:15] [INFO] 页面类型: product
[2025-08-05 09:59:15] [INFO] 商户ID: ''
[2025-08-05 09:59:15] [INFO] 商品ID: '23'
[2025-08-05 09:59:15] [INFO] 订单ID: ''
[2025-08-05 10:02:14] [INFO] === 新的访问开始 ===
[2025-08-05 10:02:14] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 10:02:14] [INFO] 请求方法: GET
[2025-08-05 10:02:14] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 10:02:14] [INFO] POST参数: []
[2025-08-05 10:02:14] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Mobile/15E148 Safari/604.1
[2025-08-05 10:02:14] [INFO] 客户端IP: **************
[2025-08-05 10:02:14] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 10:02:14] [INFO] 页面类型: merchant
[2025-08-05 10:02:14] [INFO] 商户ID: '8034567958'
[2025-08-05 10:02:14] [INFO] 商品ID: ''
[2025-08-05 10:02:14] [INFO] 订单ID: ''
[2025-08-05 10:11:34] [INFO] === 新的访问开始 ===
[2025-08-05 10:11:34] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 10:11:34] [INFO] 请求方法: GET
[2025-08-05 10:11:34] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 10:11:34] [INFO] POST参数: []
[2025-08-05 10:11:34] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; V2230A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-05 10:11:34] [INFO] 客户端IP: **************
[2025-08-05 10:11:34] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 10:11:34] [INFO] 页面类型: merchant
[2025-08-05 10:11:34] [INFO] 商户ID: '8034567958'
[2025-08-05 10:11:34] [INFO] 商品ID: ''
[2025-08-05 10:11:34] [INFO] 订单ID: ''
[2025-08-05 10:53:18] [INFO] === 新的访问开始 ===
[2025-08-05 10:53:18] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 10:53:18] [INFO] 请求方法: GET
[2025-08-05 10:53:18] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 10:53:18] [INFO] POST参数: []
[2025-08-05 10:53:18] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/*********
[2025-08-05 10:53:18] [INFO] 客户端IP: ************
[2025-08-05 10:53:18] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 10:53:18] [INFO] 页面类型: merchant
[2025-08-05 10:53:18] [INFO] 商户ID: '8034567958'
[2025-08-05 10:53:18] [INFO] 商品ID: ''
[2025-08-05 10:53:18] [INFO] 订单ID: ''
[2025-08-05 10:54:59] [INFO] === 新的访问开始 ===
[2025-08-05 10:54:59] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 10:54:59] [INFO] 请求方法: GET
[2025-08-05 10:54:59] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 10:54:59] [INFO] POST参数: []
[2025-08-05 10:54:59] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/*********
[2025-08-05 10:54:59] [INFO] 客户端IP: ************
[2025-08-05 10:54:59] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 10:54:59] [INFO] 页面类型: merchant
[2025-08-05 10:54:59] [INFO] 商户ID: '8034567958'
[2025-08-05 10:54:59] [INFO] 商品ID: ''
[2025-08-05 10:54:59] [INFO] 订单ID: ''
[2025-08-05 10:57:10] [INFO] === 新的访问开始 ===
[2025-08-05 10:57:10] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 10:57:10] [INFO] 请求方法: GET
[2025-08-05 10:57:10] [INFO] GET参数: {"sp":"23"}
[2025-08-05 10:57:10] [INFO] POST参数: []
[2025-08-05 10:57:10] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 13; zh-cn; PELM00 Build/TP1A.220905.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/*********
[2025-08-05 10:57:10] [INFO] 客户端IP: ************
[2025-08-05 10:57:10] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 10:57:10] [INFO] 页面类型: product
[2025-08-05 10:57:10] [INFO] 商户ID: ''
[2025-08-05 10:57:10] [INFO] 商品ID: '23'
[2025-08-05 10:57:10] [INFO] 订单ID: ''
[2025-08-05 11:07:59] [INFO] === 新的访问开始 ===
[2025-08-05 11:07:59] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 11:07:59] [INFO] 请求方法: GET
[2025-08-05 11:07:59] [INFO] GET参数: {"sp":"23"}
[2025-08-05 11:07:59] [INFO] POST参数: []
[2025-08-05 11:07:59] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.3.1 Mobile/22D72 Safari/604.1
[2025-08-05 11:07:59] [INFO] 客户端IP: **************
[2025-08-05 11:07:59] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 11:07:59] [INFO] 页面类型: product
[2025-08-05 11:07:59] [INFO] 商户ID: ''
[2025-08-05 11:07:59] [INFO] 商品ID: '23'
[2025-08-05 11:07:59] [INFO] 订单ID: ''
[2025-08-05 11:57:45] [INFO] === 新的访问开始 ===
[2025-08-05 11:57:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 11:57:45] [INFO] 请求方法: GET
[2025-08-05 11:57:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 11:57:45] [INFO] POST参数: []
[2025-08-05 11:57:45] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 11:57:45] [INFO] 客户端IP: **************
[2025-08-05 11:57:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 11:57:45] [INFO] 页面类型: merchant
[2025-08-05 11:57:45] [INFO] 商户ID: '8034567958'
[2025-08-05 11:57:45] [INFO] 商品ID: ''
[2025-08-05 11:57:45] [INFO] 订单ID: ''
[2025-08-05 12:02:20] [INFO] === 新的访问开始 ===
[2025-08-05 12:02:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:02:20] [INFO] 请求方法: GET
[2025-08-05 12:02:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:02:20] [INFO] POST参数: []
[2025-08-05 12:02:20] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:02:20] [INFO] 客户端IP: ************
[2025-08-05 12:02:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:02:20] [INFO] 页面类型: merchant
[2025-08-05 12:02:20] [INFO] 商户ID: '8034567958'
[2025-08-05 12:02:20] [INFO] 商品ID: ''
[2025-08-05 12:02:20] [INFO] 订单ID: ''
[2025-08-05 12:02:45] [INFO] === 新的访问开始 ===
[2025-08-05 12:02:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:02:45] [INFO] 请求方法: GET
[2025-08-05 12:02:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:02:45] [INFO] POST参数: []
[2025-08-05 12:02:45] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.6834.163 Mobile Safari/537.36
[2025-08-05 12:02:45] [INFO] 客户端IP: **************
[2025-08-05 12:02:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:02:45] [INFO] 页面类型: merchant
[2025-08-05 12:02:45] [INFO] 商户ID: '8034567958'
[2025-08-05 12:02:45] [INFO] 商品ID: ''
[2025-08-05 12:02:45] [INFO] 订单ID: ''
[2025-08-05 12:11:59] [INFO] === 新的访问开始 ===
[2025-08-05 12:11:59] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:11:59] [INFO] 请求方法: GET
[2025-08-05 12:11:59] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:11:59] [INFO] POST参数: []
[2025-08-05 12:11:59] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:11:59] [INFO] 客户端IP: **************
[2025-08-05 12:11:59] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:11:59] [INFO] 页面类型: merchant
[2025-08-05 12:11:59] [INFO] 商户ID: '8034567958'
[2025-08-05 12:11:59] [INFO] 商品ID: ''
[2025-08-05 12:11:59] [INFO] 订单ID: ''
[2025-08-05 12:12:12] [INFO] === 新的访问开始 ===
[2025-08-05 12:12:12] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:12:12] [INFO] 请求方法: GET
[2025-08-05 12:12:12] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:12:12] [INFO] POST参数: []
[2025-08-05 12:12:12] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:12:12] [INFO] 客户端IP: **************
[2025-08-05 12:12:12] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:12:12] [INFO] 页面类型: merchant
[2025-08-05 12:12:12] [INFO] 商户ID: '8034567958'
[2025-08-05 12:12:12] [INFO] 商品ID: ''
[2025-08-05 12:12:12] [INFO] 订单ID: ''
[2025-08-05 12:29:49] [INFO] === 新的访问开始 ===
[2025-08-05 12:29:49] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:29:49] [INFO] 请求方法: GET
[2025-08-05 12:29:49] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:29:49] [INFO] POST参数: []
[2025-08-05 12:29:49] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:29:49] [INFO] 客户端IP: **************
[2025-08-05 12:29:49] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:29:49] [INFO] 页面类型: merchant
[2025-08-05 12:29:49] [INFO] 商户ID: '8034567958'
[2025-08-05 12:29:49] [INFO] 商品ID: ''
[2025-08-05 12:29:49] [INFO] 订单ID: ''
[2025-08-05 12:32:29] [INFO] === 新的访问开始 ===
[2025-08-05 12:32:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:32:29] [INFO] 请求方法: GET
[2025-08-05 12:32:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:32:29] [INFO] POST参数: []
[2025-08-05 12:32:29] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:32:29] [INFO] 客户端IP: **************
[2025-08-05 12:32:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:32:29] [INFO] 页面类型: merchant
[2025-08-05 12:32:29] [INFO] 商户ID: '8034567958'
[2025-08-05 12:32:29] [INFO] 商品ID: ''
[2025-08-05 12:32:29] [INFO] 订单ID: ''
[2025-08-05 12:32:53] [INFO] === 新的访问开始 ===
[2025-08-05 12:32:53] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:32:53] [INFO] 请求方法: GET
[2025-08-05 12:32:53] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:32:53] [INFO] POST参数: []
[2025-08-05 12:32:53] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:32:53] [INFO] 客户端IP: **************
[2025-08-05 12:32:53] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:32:53] [INFO] 页面类型: merchant
[2025-08-05 12:32:53] [INFO] 商户ID: '8034567958'
[2025-08-05 12:32:53] [INFO] 商品ID: ''
[2025-08-05 12:32:53] [INFO] 订单ID: ''
[2025-08-05 12:38:16] [INFO] === 新的访问开始 ===
[2025-08-05 12:38:16] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:38:16] [INFO] 请求方法: GET
[2025-08-05 12:38:16] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:38:16] [INFO] POST参数: []
[2025-08-05 12:38:16] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:38:16] [INFO] 客户端IP: *************
[2025-08-05 12:38:16] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:38:16] [INFO] 页面类型: merchant
[2025-08-05 12:38:16] [INFO] 商户ID: '8034567958'
[2025-08-05 12:38:16] [INFO] 商品ID: ''
[2025-08-05 12:38:16] [INFO] 订单ID: ''
[2025-08-05 12:45:31] [INFO] === 新的访问开始 ===
[2025-08-05 12:45:31] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:45:31] [INFO] 请求方法: GET
[2025-08-05 12:45:31] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:45:31] [INFO] POST参数: []
[2025-08-05 12:45:31] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:45:31] [INFO] 客户端IP: **************
[2025-08-05 12:45:31] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:45:31] [INFO] 页面类型: merchant
[2025-08-05 12:45:31] [INFO] 商户ID: '8034567958'
[2025-08-05 12:45:31] [INFO] 商品ID: ''
[2025-08-05 12:45:31] [INFO] 订单ID: ''
[2025-08-05 12:47:15] [INFO] === 新的访问开始 ===
[2025-08-05 12:47:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:47:15] [INFO] 请求方法: GET
[2025-08-05 12:47:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:47:15] [INFO] POST参数: []
[2025-08-05 12:47:15] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:47:15] [INFO] 客户端IP: **************
[2025-08-05 12:47:15] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:47:15] [INFO] 页面类型: merchant
[2025-08-05 12:47:15] [INFO] 商户ID: '8034567958'
[2025-08-05 12:47:15] [INFO] 商品ID: ''
[2025-08-05 12:47:15] [INFO] 订单ID: ''
[2025-08-05 12:51:28] [INFO] === 新的访问开始 ===
[2025-08-05 12:51:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:51:28] [INFO] 请求方法: GET
[2025-08-05 12:51:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:51:28] [INFO] POST参数: []
[2025-08-05 12:51:28] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-05 12:51:28] [INFO] 客户端IP: **************
[2025-08-05 12:51:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:51:28] [INFO] 页面类型: merchant
[2025-08-05 12:51:28] [INFO] 商户ID: '8034567958'
[2025-08-05 12:51:28] [INFO] 商品ID: ''
[2025-08-05 12:51:28] [INFO] 订单ID: ''
[2025-08-05 12:52:18] [INFO] === 新的访问开始 ===
[2025-08-05 12:52:18] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:52:18] [INFO] 请求方法: GET
[2025-08-05 12:52:18] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:52:18] [INFO] POST参数: []
[2025-08-05 12:52:18] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-05 12:52:18] [INFO] 客户端IP: **************
[2025-08-05 12:52:18] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:52:18] [INFO] 页面类型: merchant
[2025-08-05 12:52:18] [INFO] 商户ID: '8034567958'
[2025-08-05 12:52:18] [INFO] 商品ID: ''
[2025-08-05 12:52:18] [INFO] 订单ID: ''
[2025-08-05 12:54:09] [INFO] === 新的访问开始 ===
[2025-08-05 12:54:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 12:54:09] [INFO] 请求方法: GET
[2025-08-05 12:54:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 12:54:09] [INFO] POST参数: []
[2025-08-05 12:54:09] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-05 12:54:09] [INFO] 客户端IP: **************
[2025-08-05 12:54:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 12:54:09] [INFO] 页面类型: merchant
[2025-08-05 12:54:09] [INFO] 商户ID: '8034567958'
[2025-08-05 12:54:09] [INFO] 商品ID: ''
[2025-08-05 12:54:09] [INFO] 订单ID: ''
[2025-08-05 13:12:40] [INFO] === 新的访问开始 ===
[2025-08-05 13:12:40] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 13:12:40] [INFO] 请求方法: GET
[2025-08-05 13:12:40] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 13:12:40] [INFO] POST参数: []
[2025-08-05 13:12:40] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/19E241 Safari/604.1
[2025-08-05 13:12:40] [INFO] 客户端IP: *************
[2025-08-05 13:12:40] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 13:12:40] [INFO] 页面类型: merchant
[2025-08-05 13:12:40] [INFO] 商户ID: '8034567958'
[2025-08-05 13:12:40] [INFO] 商品ID: ''
[2025-08-05 13:12:40] [INFO] 订单ID: ''
[2025-08-05 13:15:20] [INFO] === 新的访问开始 ===
[2025-08-05 13:15:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 13:15:20] [INFO] 请求方法: GET
[2025-08-05 13:15:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 13:15:20] [INFO] POST参数: []
[2025-08-05 13:15:20] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-05 13:15:20] [INFO] 客户端IP: ************
[2025-08-05 13:15:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 13:15:20] [INFO] 页面类型: merchant
[2025-08-05 13:15:20] [INFO] 商户ID: '8034567958'
[2025-08-05 13:15:20] [INFO] 商品ID: ''
[2025-08-05 13:15:20] [INFO] 订单ID: ''
[2025-08-05 13:17:26] [INFO] === 新的访问开始 ===
[2025-08-05 13:17:26] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 13:17:26] [INFO] 请求方法: GET
[2025-08-05 13:17:26] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 13:17:26] [INFO] POST参数: []
[2025-08-05 13:17:26] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-05 13:17:26] [INFO] 客户端IP: **************
[2025-08-05 13:17:26] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 13:17:26] [INFO] 页面类型: merchant
[2025-08-05 13:17:26] [INFO] 商户ID: '8034567958'
[2025-08-05 13:17:26] [INFO] 商品ID: ''
[2025-08-05 13:17:26] [INFO] 订单ID: ''
[2025-08-05 13:18:09] [INFO] === 新的访问开始 ===
[2025-08-05 13:18:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 13:18:09] [INFO] 请求方法: GET
[2025-08-05 13:18:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 13:18:09] [INFO] POST参数: []
[2025-08-05 13:18:09] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-05 13:18:09] [INFO] 客户端IP: **************
[2025-08-05 13:18:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 13:18:09] [INFO] 页面类型: merchant
[2025-08-05 13:18:09] [INFO] 商户ID: '8034567958'
[2025-08-05 13:18:09] [INFO] 商品ID: ''
[2025-08-05 13:18:09] [INFO] 订单ID: ''
[2025-08-05 13:21:19] [INFO] === 新的访问开始 ===
[2025-08-05 13:21:19] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 13:21:19] [INFO] 请求方法: GET
[2025-08-05 13:21:19] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 13:21:19] [INFO] POST参数: []
[2025-08-05 13:21:19] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 13:21:19] [INFO] 客户端IP: **************
[2025-08-05 13:21:19] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 13:21:19] [INFO] 页面类型: merchant
[2025-08-05 13:21:19] [INFO] 商户ID: '8034567958'
[2025-08-05 13:21:19] [INFO] 商品ID: ''
[2025-08-05 13:21:19] [INFO] 订单ID: ''
[2025-08-05 13:22:15] [INFO] === 新的访问开始 ===
[2025-08-05 13:22:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 13:22:15] [INFO] 请求方法: GET
[2025-08-05 13:22:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 13:22:15] [INFO] POST参数: []
[2025-08-05 13:22:15] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; V2230A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-05 13:22:15] [INFO] 客户端IP: **************
[2025-08-05 13:22:15] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 13:22:15] [INFO] 页面类型: merchant
[2025-08-05 13:22:15] [INFO] 商户ID: '8034567958'
[2025-08-05 13:22:15] [INFO] 商品ID: ''
[2025-08-05 13:22:15] [INFO] 订单ID: ''
[2025-08-05 14:00:25] [INFO] === 新的访问开始 ===
[2025-08-05 14:00:25] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:00:25] [INFO] 请求方法: GET
[2025-08-05 14:00:25] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:00:25] [INFO] POST参数: []
[2025-08-05 14:00:25] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; HarmonyOS; SEA-AL10; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-05 14:00:25] [INFO] 客户端IP: *************
[2025-08-05 14:00:25] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:00:25] [INFO] 页面类型: merchant
[2025-08-05 14:00:25] [INFO] 商户ID: '8034567958'
[2025-08-05 14:00:25] [INFO] 商品ID: ''
[2025-08-05 14:00:25] [INFO] 订单ID: ''
[2025-08-05 14:00:25] [INFO] === 新的访问开始 ===
[2025-08-05 14:00:25] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:00:25] [INFO] 请求方法: GET
[2025-08-05 14:00:25] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:00:25] [INFO] POST参数: []
[2025-08-05 14:00:25] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; HarmonyOS; SEA-AL10; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-05 14:00:25] [INFO] 客户端IP: *************
[2025-08-05 14:00:25] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:00:25] [INFO] 页面类型: merchant
[2025-08-05 14:00:25] [INFO] 商户ID: '8034567958'
[2025-08-05 14:00:25] [INFO] 商品ID: ''
[2025-08-05 14:00:25] [INFO] 订单ID: ''
[2025-08-05 14:05:58] [INFO] === 新的访问开始 ===
[2025-08-05 14:05:58] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:05:58] [INFO] 请求方法: GET
[2025-08-05 14:05:58] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:05:58] [INFO] POST参数: []
[2025-08-05 14:05:58] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 14:05:58] [INFO] 客户端IP: **************
[2025-08-05 14:05:58] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:05:58] [INFO] 页面类型: merchant
[2025-08-05 14:05:58] [INFO] 商户ID: '8034567958'
[2025-08-05 14:05:58] [INFO] 商品ID: ''
[2025-08-05 14:05:58] [INFO] 订单ID: ''
[2025-08-05 14:12:21] [INFO] === 新的访问开始 ===
[2025-08-05 14:12:21] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 14:12:21] [INFO] 请求方法: GET
[2025-08-05 14:12:21] [INFO] GET参数: {"sp":"23"}
[2025-08-05 14:12:21] [INFO] POST参数: []
[2025-08-05 14:12:21] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; K) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Safari/537.36
[2025-08-05 14:12:21] [INFO] 客户端IP: *************
[2025-08-05 14:12:21] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 14:12:21] [INFO] 页面类型: product
[2025-08-05 14:12:21] [INFO] 商户ID: ''
[2025-08-05 14:12:21] [INFO] 商品ID: '23'
[2025-08-05 14:12:21] [INFO] 订单ID: ''
[2025-08-05 14:12:30] [INFO] === 新的访问开始 ===
[2025-08-05 14:12:30] [INFO] 请求URI: /shop.php?dd=123456
[2025-08-05 14:12:30] [INFO] 请求方法: GET
[2025-08-05 14:12:30] [INFO] GET参数: {"dd":"123456"}
[2025-08-05 14:12:30] [INFO] POST参数: []
[2025-08-05 14:12:30] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; K) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Safari/537.36
[2025-08-05 14:12:30] [INFO] 客户端IP: *************
[2025-08-05 14:12:30] [INFO] 解析参数 - sj: '', sp: '', dd: '123456'
[2025-08-05 14:12:30] [INFO] 页面类型: order
[2025-08-05 14:12:30] [INFO] 商户ID: ''
[2025-08-05 14:12:30] [INFO] 商品ID: ''
[2025-08-05 14:12:30] [INFO] 订单ID: '123456'
[2025-08-05 14:12:41] [INFO] === 新的访问开始 ===
[2025-08-05 14:12:41] [INFO] 请求URI: /shop.php?dd=123456789
[2025-08-05 14:12:41] [INFO] 请求方法: GET
[2025-08-05 14:12:41] [INFO] GET参数: {"dd":"123456789"}
[2025-08-05 14:12:41] [INFO] POST参数: []
[2025-08-05 14:12:41] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; K) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Safari/537.36
[2025-08-05 14:12:41] [INFO] 客户端IP: *************
[2025-08-05 14:12:41] [INFO] 解析参数 - sj: '', sp: '', dd: '123456789'
[2025-08-05 14:12:41] [INFO] 页面类型: order
[2025-08-05 14:12:41] [INFO] 商户ID: ''
[2025-08-05 14:12:41] [INFO] 商品ID: ''
[2025-08-05 14:12:41] [INFO] 订单ID: '123456789'
[2025-08-05 14:21:20] [INFO] === 新的访问开始 ===
[2025-08-05 14:21:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:21:20] [INFO] 请求方法: GET
[2025-08-05 14:21:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:21:20] [INFO] POST参数: []
[2025-08-05 14:21:20] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 14:21:20] [INFO] 客户端IP: **************
[2025-08-05 14:21:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:21:20] [INFO] 页面类型: merchant
[2025-08-05 14:21:20] [INFO] 商户ID: '8034567958'
[2025-08-05 14:21:20] [INFO] 商品ID: ''
[2025-08-05 14:21:20] [INFO] 订单ID: ''
[2025-08-05 14:25:02] [INFO] === 新的访问开始 ===
[2025-08-05 14:25:02] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 14:25:02] [INFO] 请求方法: GET
[2025-08-05 14:25:02] [INFO] GET参数: {"sp":"23"}
[2025-08-05 14:25:02] [INFO] POST参数: []
[2025-08-05 14:25:02] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-05 14:25:02] [INFO] 客户端IP: ************
[2025-08-05 14:25:02] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 14:25:02] [INFO] 页面类型: product
[2025-08-05 14:25:02] [INFO] 商户ID: ''
[2025-08-05 14:25:02] [INFO] 商品ID: '23'
[2025-08-05 14:25:02] [INFO] 订单ID: ''
[2025-08-05 14:32:26] [INFO] === 新的访问开始 ===
[2025-08-05 14:32:26] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:32:26] [INFO] 请求方法: GET
[2025-08-05 14:32:26] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:32:26] [INFO] POST参数: []
[2025-08-05 14:32:26] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Mobile/21G93 Safari/604.1
[2025-08-05 14:32:26] [INFO] 客户端IP: *************
[2025-08-05 14:32:26] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:32:26] [INFO] 页面类型: merchant
[2025-08-05 14:32:26] [INFO] 商户ID: '8034567958'
[2025-08-05 14:32:26] [INFO] 商品ID: ''
[2025-08-05 14:32:26] [INFO] 订单ID: ''
[2025-08-05 14:34:02] [INFO] === 新的访问开始 ===
[2025-08-05 14:34:02] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:34:02] [INFO] 请求方法: GET
[2025-08-05 14:34:02] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:34:02] [INFO] POST参数: []
[2025-08-05 14:34:02] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Mobile/21G93 Safari/604.1
[2025-08-05 14:34:02] [INFO] 客户端IP: **************
[2025-08-05 14:34:02] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:34:02] [INFO] 页面类型: merchant
[2025-08-05 14:34:02] [INFO] 商户ID: '8034567958'
[2025-08-05 14:34:02] [INFO] 商品ID: ''
[2025-08-05 14:34:02] [INFO] 订单ID: ''
[2025-08-05 14:34:09] [INFO] === 新的访问开始 ===
[2025-08-05 14:34:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:34:09] [INFO] 请求方法: GET
[2025-08-05 14:34:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:34:09] [INFO] POST参数: []
[2025-08-05 14:34:09] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138.0.7204.119 Mobile/15E148 Safari/604.1
[2025-08-05 14:34:09] [INFO] 客户端IP: ************
[2025-08-05 14:34:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:34:09] [INFO] 页面类型: merchant
[2025-08-05 14:34:09] [INFO] 商户ID: '8034567958'
[2025-08-05 14:34:09] [INFO] 商品ID: ''
[2025-08-05 14:34:09] [INFO] 订单ID: ''
[2025-08-05 14:40:59] [INFO] === 新的访问开始 ===
[2025-08-05 14:40:59] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:40:59] [INFO] 请求方法: GET
[2025-08-05 14:40:59] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:40:59] [INFO] POST参数: []
[2025-08-05 14:40:59] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-05 14:40:59] [INFO] 客户端IP: ************
[2025-08-05 14:40:59] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:40:59] [INFO] 页面类型: merchant
[2025-08-05 14:40:59] [INFO] 商户ID: '8034567958'
[2025-08-05 14:40:59] [INFO] 商品ID: ''
[2025-08-05 14:40:59] [INFO] 订单ID: ''
[2025-08-05 14:42:48] [INFO] === 新的访问开始 ===
[2025-08-05 14:42:48] [INFO] 请求URI: /shop.php?dd=ORDER17542959174744
[2025-08-05 14:42:48] [INFO] 请求方法: GET
[2025-08-05 14:42:48] [INFO] GET参数: {"dd":"ORDER17542959174744"}
[2025-08-05 14:42:48] [INFO] POST参数: []
[2025-08-05 14:42:48] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-05 14:42:48] [INFO] 客户端IP: ************
[2025-08-05 14:42:48] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17542959174744'
[2025-08-05 14:42:48] [INFO] 页面类型: order
[2025-08-05 14:42:48] [INFO] 商户ID: ''
[2025-08-05 14:42:48] [INFO] 商品ID: ''
[2025-08-05 14:42:48] [INFO] 订单ID: 'ORDER17542959174744'
[2025-08-05 14:50:41] [INFO] === 新的访问开始 ===
[2025-08-05 14:50:41] [INFO] 请求URI: /shop.php?dd=ORDER17542959174744
[2025-08-05 14:50:41] [INFO] 请求方法: GET
[2025-08-05 14:50:41] [INFO] GET参数: {"dd":"ORDER17542959174744"}
[2025-08-05 14:50:41] [INFO] POST参数: []
[2025-08-05 14:50:41] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-05 14:50:41] [INFO] 客户端IP: ************
[2025-08-05 14:50:41] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17542959174744'
[2025-08-05 14:50:41] [INFO] 页面类型: order
[2025-08-05 14:50:41] [INFO] 商户ID: ''
[2025-08-05 14:50:41] [INFO] 商品ID: ''
[2025-08-05 14:50:41] [INFO] 订单ID: 'ORDER17542959174744'
[2025-08-05 14:55:44] [INFO] === 新的访问开始 ===
[2025-08-05 14:55:44] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 14:55:44] [INFO] 请求方法: GET
[2025-08-05 14:55:44] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 14:55:44] [INFO] POST参数: []
[2025-08-05 14:55:44] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-05 14:55:44] [INFO] 客户端IP: ************
[2025-08-05 14:55:44] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 14:55:44] [INFO] 页面类型: merchant
[2025-08-05 14:55:44] [INFO] 商户ID: '8034567958'
[2025-08-05 14:55:44] [INFO] 商品ID: ''
[2025-08-05 14:55:44] [INFO] 订单ID: ''
[2025-08-05 15:03:38] [INFO] === 新的访问开始 ===
[2025-08-05 15:03:38] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 15:03:38] [INFO] 请求方法: GET
[2025-08-05 15:03:38] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 15:03:38] [INFO] POST参数: []
[2025-08-05 15:03:38] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Mobile/21G93 Safari/604.1
[2025-08-05 15:03:38] [INFO] 客户端IP: **************
[2025-08-05 15:03:38] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 15:03:38] [INFO] 页面类型: merchant
[2025-08-05 15:03:38] [INFO] 商户ID: '8034567958'
[2025-08-05 15:03:38] [INFO] 商品ID: ''
[2025-08-05 15:03:38] [INFO] 订单ID: ''
[2025-08-05 15:04:36] [INFO] === 新的访问开始 ===
[2025-08-05 15:04:36] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 15:04:36] [INFO] 请求方法: GET
[2025-08-05 15:04:36] [INFO] GET参数: {"sp":"23"}
[2025-08-05 15:04:36] [INFO] POST参数: []
[2025-08-05 15:04:36] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6.1 Mobile/21G93 Safari/604.1
[2025-08-05 15:04:36] [INFO] 客户端IP: **************
[2025-08-05 15:04:36] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 15:04:36] [INFO] 页面类型: product
[2025-08-05 15:04:36] [INFO] 商户ID: ''
[2025-08-05 15:04:36] [INFO] 商品ID: '23'
[2025-08-05 15:04:36] [INFO] 订单ID: ''
[2025-08-05 15:17:25] [INFO] === 新的访问开始 ===
[2025-08-05 15:17:25] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 15:17:25] [INFO] 请求方法: GET
[2025-08-05 15:17:25] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 15:17:25] [INFO] POST参数: []
[2025-08-05 15:17:25] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 15:17:25] [INFO] 客户端IP: **************
[2025-08-05 15:17:25] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 15:17:25] [INFO] 页面类型: merchant
[2025-08-05 15:17:25] [INFO] 商户ID: '8034567958'
[2025-08-05 15:17:25] [INFO] 商品ID: ''
[2025-08-05 15:17:25] [INFO] 订单ID: ''
[2025-08-05 15:20:57] [INFO] === 新的访问开始 ===
[2025-08-05 15:20:57] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 15:20:57] [INFO] 请求方法: GET
[2025-08-05 15:20:57] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 15:20:57] [INFO] POST参数: []
[2025-08-05 15:20:57] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 15:20:57] [INFO] 客户端IP: **************
[2025-08-05 15:20:57] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 15:20:57] [INFO] 页面类型: merchant
[2025-08-05 15:20:57] [INFO] 商户ID: '8034567958'
[2025-08-05 15:20:57] [INFO] 商品ID: ''
[2025-08-05 15:20:57] [INFO] 订单ID: ''
[2025-08-05 15:22:41] [INFO] === 新的访问开始 ===
[2025-08-05 15:22:41] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 15:22:41] [INFO] 请求方法: GET
[2025-08-05 15:22:41] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 15:22:41] [INFO] POST参数: []
[2025-08-05 15:22:41] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 15:22:41] [INFO] 客户端IP: **************
[2025-08-05 15:22:41] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 15:22:41] [INFO] 页面类型: merchant
[2025-08-05 15:22:41] [INFO] 商户ID: '8034567958'
[2025-08-05 15:22:41] [INFO] 商品ID: ''
[2025-08-05 15:22:41] [INFO] 订单ID: ''
[2025-08-05 15:26:51] [INFO] === 新的访问开始 ===
[2025-08-05 15:26:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 15:26:51] [INFO] 请求方法: GET
[2025-08-05 15:26:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 15:26:51] [INFO] POST参数: []
[2025-08-05 15:26:51] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 15:26:51] [INFO] 客户端IP: **************
[2025-08-05 15:26:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 15:26:51] [INFO] 页面类型: merchant
[2025-08-05 15:26:51] [INFO] 商户ID: '8034567958'
[2025-08-05 15:26:51] [INFO] 商品ID: ''
[2025-08-05 15:26:51] [INFO] 订单ID: ''
[2025-08-05 15:28:03] [INFO] === 新的访问开始 ===
[2025-08-05 15:28:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 15:28:03] [INFO] 请求方法: GET
[2025-08-05 15:28:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 15:28:03] [INFO] POST参数: []
[2025-08-05 15:28:03] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 15:28:03] [INFO] 客户端IP: **************
[2025-08-05 15:28:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 15:28:03] [INFO] 页面类型: merchant
[2025-08-05 15:28:03] [INFO] 商户ID: '8034567958'
[2025-08-05 15:28:03] [INFO] 商品ID: ''
[2025-08-05 15:28:03] [INFO] 订单ID: ''
[2025-08-05 15:30:34] [INFO] === 新的访问开始 ===
[2025-08-05 15:30:34] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 15:30:34] [INFO] 请求方法: GET
[2025-08-05 15:30:34] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 15:30:34] [INFO] POST参数: []
[2025-08-05 15:30:34] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 15:30:34] [INFO] 客户端IP: **************
[2025-08-05 15:30:34] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 15:30:34] [INFO] 页面类型: merchant
[2025-08-05 15:30:34] [INFO] 商户ID: '8034567958'
[2025-08-05 15:30:34] [INFO] 商品ID: ''
[2025-08-05 15:30:34] [INFO] 订单ID: ''
[2025-08-05 16:11:52] [INFO] === 新的访问开始 ===
[2025-08-05 16:11:52] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 16:11:52] [INFO] 请求方法: GET
[2025-08-05 16:11:52] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 16:11:52] [INFO] POST参数: []
[2025-08-05 16:11:52] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 16:11:52] [INFO] 客户端IP: **************
[2025-08-05 16:11:52] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 16:11:52] [INFO] 页面类型: merchant
[2025-08-05 16:11:52] [INFO] 商户ID: '8034567958'
[2025-08-05 16:11:52] [INFO] 商品ID: ''
[2025-08-05 16:11:52] [INFO] 订单ID: ''
[2025-08-05 16:12:38] [INFO] === 新的访问开始 ===
[2025-08-05 16:12:38] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 16:12:38] [INFO] 请求方法: GET
[2025-08-05 16:12:38] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 16:12:38] [INFO] POST参数: []
[2025-08-05 16:12:38] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 16:12:38] [INFO] 客户端IP: **************
[2025-08-05 16:12:38] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 16:12:38] [INFO] 页面类型: merchant
[2025-08-05 16:12:38] [INFO] 商户ID: '8034567958'
[2025-08-05 16:12:38] [INFO] 商品ID: ''
[2025-08-05 16:12:38] [INFO] 订单ID: ''
[2025-08-05 18:30:47] [INFO] === 新的访问开始 ===
[2025-08-05 18:30:47] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 18:30:47] [INFO] 请求方法: GET
[2025-08-05 18:30:47] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 18:30:47] [INFO] POST参数: []
[2025-08-05 18:30:47] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-05 18:30:47] [INFO] 客户端IP: **************
[2025-08-05 18:30:47] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 18:30:47] [INFO] 页面类型: merchant
[2025-08-05 18:30:47] [INFO] 商户ID: '8034567958'
[2025-08-05 18:30:47] [INFO] 商品ID: ''
[2025-08-05 18:30:47] [INFO] 订单ID: ''
[2025-08-05 19:06:28] [INFO] === 新的访问开始 ===
[2025-08-05 19:06:28] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 19:06:28] [INFO] 请求方法: GET
[2025-08-05 19:06:28] [INFO] GET参数: {"sp":"23"}
[2025-08-05 19:06:28] [INFO] POST参数: []
[2025-08-05 19:06:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-05 19:06:28] [INFO] 客户端IP: **************
[2025-08-05 19:06:28] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 19:06:28] [INFO] 页面类型: product
[2025-08-05 19:06:28] [INFO] 商户ID: ''
[2025-08-05 19:06:28] [INFO] 商品ID: '23'
[2025-08-05 19:06:28] [INFO] 订单ID: ''
[2025-08-05 22:28:51] [INFO] === 新的访问开始 ===
[2025-08-05 22:28:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 22:28:51] [INFO] 请求方法: GET
[2025-08-05 22:28:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 22:28:51] [INFO] POST参数: []
[2025-08-05 22:28:51] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-05 22:28:51] [INFO] 客户端IP: **************
[2025-08-05 22:28:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 22:28:51] [INFO] 页面类型: merchant
[2025-08-05 22:28:51] [INFO] 商户ID: '8034567958'
[2025-08-05 22:28:51] [INFO] 商品ID: ''
[2025-08-05 22:28:51] [INFO] 订单ID: ''
[2025-08-05 22:42:24] [INFO] === 新的访问开始 ===
[2025-08-05 22:42:24] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 22:42:24] [INFO] 请求方法: GET
[2025-08-05 22:42:24] [INFO] GET参数: {"sp":"23"}
[2025-08-05 22:42:24] [INFO] POST参数: []
[2025-08-05 22:42:24] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5.1 Mobile/21F90 Safari/604.1
[2025-08-05 22:42:24] [INFO] 客户端IP: **************
[2025-08-05 22:42:24] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 22:42:24] [INFO] 页面类型: product
[2025-08-05 22:42:24] [INFO] 商户ID: ''
[2025-08-05 22:42:24] [INFO] 商品ID: '23'
[2025-08-05 22:42:24] [INFO] 订单ID: ''
[2025-08-05 22:45:38] [INFO] === 新的访问开始 ===
[2025-08-05 22:45:38] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 22:45:38] [INFO] 请求方法: GET
[2025-08-05 22:45:38] [INFO] GET参数: {"sp":"23"}
[2025-08-05 22:45:38] [INFO] POST参数: []
[2025-08-05 22:45:38] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-05 22:45:38] [INFO] 客户端IP: **************
[2025-08-05 22:45:38] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 22:45:38] [INFO] 页面类型: product
[2025-08-05 22:45:38] [INFO] 商户ID: ''
[2025-08-05 22:45:38] [INFO] 商品ID: '23'
[2025-08-05 22:45:38] [INFO] 订单ID: ''
[2025-08-05 22:47:31] [INFO] === 新的访问开始 ===
[2025-08-05 22:47:31] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 22:47:31] [INFO] 请求方法: GET
[2025-08-05 22:47:31] [INFO] GET参数: {"sp":"23"}
[2025-08-05 22:47:31] [INFO] POST参数: []
[2025-08-05 22:47:31] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-05 22:47:31] [INFO] 客户端IP: **************
[2025-08-05 22:47:31] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 22:47:31] [INFO] 页面类型: product
[2025-08-05 22:47:31] [INFO] 商户ID: ''
[2025-08-05 22:47:31] [INFO] 商品ID: '23'
[2025-08-05 22:47:31] [INFO] 订单ID: ''
[2025-08-05 22:47:36] [INFO] === 新的访问开始 ===
[2025-08-05 22:47:36] [INFO] 请求URI: /shop.php?dd=1gR0WlA6w4XGIYE
[2025-08-05 22:47:36] [INFO] 请求方法: GET
[2025-08-05 22:47:36] [INFO] GET参数: {"dd":"1gR0WlA6w4XGIYE"}
[2025-08-05 22:47:36] [INFO] POST参数: []
[2025-08-05 22:47:36] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-05 22:47:36] [INFO] 客户端IP: **************
[2025-08-05 22:47:36] [INFO] 解析参数 - sj: '', sp: '', dd: '1gR0WlA6w4XGIYE'
[2025-08-05 22:47:36] [INFO] 页面类型: order
[2025-08-05 22:47:36] [INFO] 商户ID: ''
[2025-08-05 22:47:36] [INFO] 商品ID: ''
[2025-08-05 22:47:36] [INFO] 订单ID: '1gR0WlA6w4XGIYE'
[2025-08-05 22:48:57] [INFO] === 新的访问开始 ===
[2025-08-05 22:48:57] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 22:48:57] [INFO] 请求方法: GET
[2025-08-05 22:48:57] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 22:48:57] [INFO] POST参数: []
[2025-08-05 22:48:57] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 22:48:57] [INFO] 客户端IP: **************
[2025-08-05 22:48:57] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 22:48:57] [INFO] 页面类型: merchant
[2025-08-05 22:48:57] [INFO] 商户ID: '8034567958'
[2025-08-05 22:48:57] [INFO] 商品ID: ''
[2025-08-05 22:48:57] [INFO] 订单ID: ''
[2025-08-05 22:49:56] [INFO] === 新的访问开始 ===
[2025-08-05 22:49:56] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 22:49:56] [INFO] 请求方法: GET
[2025-08-05 22:49:56] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 22:49:56] [INFO] POST参数: []
[2025-08-05 22:49:56] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 22:49:56] [INFO] 客户端IP: **************
[2025-08-05 22:49:56] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 22:49:56] [INFO] 页面类型: merchant
[2025-08-05 22:49:56] [INFO] 商户ID: '8034567958'
[2025-08-05 22:49:56] [INFO] 商品ID: ''
[2025-08-05 22:49:56] [INFO] 订单ID: ''
[2025-08-05 22:50:14] [INFO] === 新的访问开始 ===
[2025-08-05 22:50:14] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 22:50:14] [INFO] 请求方法: GET
[2025-08-05 22:50:14] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 22:50:14] [INFO] POST参数: []
[2025-08-05 22:50:14] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 22:50:14] [INFO] 客户端IP: **************
[2025-08-05 22:50:14] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 22:50:14] [INFO] 页面类型: merchant
[2025-08-05 22:50:14] [INFO] 商户ID: '8034567958'
[2025-08-05 22:50:14] [INFO] 商品ID: ''
[2025-08-05 22:50:14] [INFO] 订单ID: ''
[2025-08-05 22:50:29] [INFO] === 新的访问开始 ===
[2025-08-05 22:50:29] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 22:50:29] [INFO] 请求方法: GET
[2025-08-05 22:50:29] [INFO] GET参数: {"sp":"23"}
[2025-08-05 22:50:29] [INFO] POST参数: []
[2025-08-05 22:50:29] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-05 22:50:29] [INFO] 客户端IP: **************
[2025-08-05 22:50:29] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 22:50:29] [INFO] 页面类型: product
[2025-08-05 22:50:29] [INFO] 商户ID: ''
[2025-08-05 22:50:29] [INFO] 商品ID: '23'
[2025-08-05 22:50:29] [INFO] 订单ID: ''
[2025-08-05 22:50:39] [INFO] === 新的访问开始 ===
[2025-08-05 22:50:39] [INFO] 请求URI: /shop.php?dd=1gR0WlA6w4XGIYE
[2025-08-05 22:50:39] [INFO] 请求方法: GET
[2025-08-05 22:50:39] [INFO] GET参数: {"dd":"1gR0WlA6w4XGIYE"}
[2025-08-05 22:50:39] [INFO] POST参数: []
[2025-08-05 22:50:39] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-05 22:50:39] [INFO] 客户端IP: **************
[2025-08-05 22:50:39] [INFO] 解析参数 - sj: '', sp: '', dd: '1gR0WlA6w4XGIYE'
[2025-08-05 22:50:39] [INFO] 页面类型: order
[2025-08-05 22:50:39] [INFO] 商户ID: ''
[2025-08-05 22:50:39] [INFO] 商品ID: ''
[2025-08-05 22:50:39] [INFO] 订单ID: '1gR0WlA6w4XGIYE'
[2025-08-05 22:51:31] [INFO] === 新的访问开始 ===
[2025-08-05 22:51:31] [INFO] 请求URI: /shop.php?dd=ORDER17544051539894
[2025-08-05 22:51:31] [INFO] 请求方法: GET
[2025-08-05 22:51:31] [INFO] GET参数: {"dd":"ORDER17544051539894"}
[2025-08-05 22:51:31] [INFO] POST参数: []
[2025-08-05 22:51:31] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-05 22:51:31] [INFO] 客户端IP: **************
[2025-08-05 22:51:31] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544051539894'
[2025-08-05 22:51:31] [INFO] 页面类型: order
[2025-08-05 22:51:31] [INFO] 商户ID: ''
[2025-08-05 22:51:31] [INFO] 商品ID: ''
[2025-08-05 22:51:31] [INFO] 订单ID: 'ORDER17544051539894'
[2025-08-05 23:04:54] [INFO] === 新的访问开始 ===
[2025-08-05 23:04:54] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:04:54] [INFO] 请求方法: GET
[2025-08-05 23:04:54] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:04:54] [INFO] POST参数: []
[2025-08-05 23:04:54] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 23:04:54] [INFO] 客户端IP: ************
[2025-08-05 23:04:54] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:04:54] [INFO] 页面类型: merchant
[2025-08-05 23:04:54] [INFO] 商户ID: '8034567958'
[2025-08-05 23:04:54] [INFO] 商品ID: ''
[2025-08-05 23:04:54] [INFO] 订单ID: ''
[2025-08-05 23:08:21] [INFO] === 新的访问开始 ===
[2025-08-05 23:08:21] [INFO] 请求URI: /shop.php?sp=23
[2025-08-05 23:08:21] [INFO] 请求方法: GET
[2025-08-05 23:08:21] [INFO] GET参数: {"sp":"23"}
[2025-08-05 23:08:21] [INFO] POST参数: []
[2025-08-05 23:08:21] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5.1 Mobile/21F90 Safari/604.1
[2025-08-05 23:08:21] [INFO] 客户端IP: *************
[2025-08-05 23:08:21] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-05 23:08:21] [INFO] 页面类型: product
[2025-08-05 23:08:21] [INFO] 商户ID: ''
[2025-08-05 23:08:21] [INFO] 商品ID: '23'
[2025-08-05 23:08:21] [INFO] 订单ID: ''
[2025-08-05 23:08:28] [INFO] === 新的访问开始 ===
[2025-08-05 23:08:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:08:28] [INFO] 请求方法: GET
[2025-08-05 23:08:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:08:28] [INFO] POST参数: []
[2025-08-05 23:08:28] [INFO] User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Safari/537.36
[2025-08-05 23:08:28] [INFO] 客户端IP: **************
[2025-08-05 23:08:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:08:28] [INFO] 页面类型: merchant
[2025-08-05 23:08:28] [INFO] 商户ID: '8034567958'
[2025-08-05 23:08:28] [INFO] 商品ID: ''
[2025-08-05 23:08:28] [INFO] 订单ID: ''
[2025-08-05 23:11:09] [INFO] === 新的访问开始 ===
[2025-08-05 23:11:09] [INFO] 请求URI: /shop.php?dd=ORDER17544065074616
[2025-08-05 23:11:09] [INFO] 请求方法: GET
[2025-08-05 23:11:09] [INFO] GET参数: {"dd":"ORDER17544065074616"}
[2025-08-05 23:11:09] [INFO] POST参数: []
[2025-08-05 23:11:09] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 23:11:09] [INFO] 客户端IP: **************
[2025-08-05 23:11:09] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544065074616'
[2025-08-05 23:11:09] [INFO] 页面类型: order
[2025-08-05 23:11:09] [INFO] 商户ID: ''
[2025-08-05 23:11:09] [INFO] 商品ID: ''
[2025-08-05 23:11:09] [INFO] 订单ID: 'ORDER17544065074616'
[2025-08-05 23:11:21] [INFO] === 新的访问开始 ===
[2025-08-05 23:11:21] [INFO] 请求URI: /shop.php?dd=ORDER17544065074616
[2025-08-05 23:11:21] [INFO] 请求方法: GET
[2025-08-05 23:11:21] [INFO] GET参数: {"dd":"ORDER17544065074616"}
[2025-08-05 23:11:21] [INFO] POST参数: []
[2025-08-05 23:11:21] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-05 23:11:21] [INFO] 客户端IP: **************
[2025-08-05 23:11:21] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544065074616'
[2025-08-05 23:11:21] [INFO] 页面类型: order
[2025-08-05 23:11:21] [INFO] 商户ID: ''
[2025-08-05 23:11:21] [INFO] 商品ID: ''
[2025-08-05 23:11:21] [INFO] 订单ID: 'ORDER17544065074616'
[2025-08-05 23:19:51] [INFO] === 新的访问开始 ===
[2025-08-05 23:19:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:19:51] [INFO] 请求方法: GET
[2025-08-05 23:19:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:19:51] [INFO] POST参数: []
[2025-08-05 23:19:51] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4 Mobile/15E148 Safari/604.1
[2025-08-05 23:19:51] [INFO] 客户端IP: **************
[2025-08-05 23:19:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:19:51] [INFO] 页面类型: merchant
[2025-08-05 23:19:51] [INFO] 商户ID: '8034567958'
[2025-08-05 23:19:51] [INFO] 商品ID: ''
[2025-08-05 23:19:51] [INFO] 订单ID: ''
[2025-08-05 23:26:37] [INFO] === 新的访问开始 ===
[2025-08-05 23:26:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:26:37] [INFO] 请求方法: GET
[2025-08-05 23:26:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:26:37] [INFO] POST参数: []
[2025-08-05 23:26:37] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-05 23:26:37] [INFO] 客户端IP: *************
[2025-08-05 23:26:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:26:37] [INFO] 页面类型: merchant
[2025-08-05 23:26:37] [INFO] 商户ID: '8034567958'
[2025-08-05 23:26:37] [INFO] 商品ID: ''
[2025-08-05 23:26:37] [INFO] 订单ID: ''
[2025-08-05 23:26:41] [INFO] === 新的访问开始 ===
[2025-08-05 23:26:41] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:26:41] [INFO] 请求方法: GET
[2025-08-05 23:26:41] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:26:41] [INFO] POST参数: []
[2025-08-05 23:26:41] [INFO] User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138 Version/11.1.1 Safari/605.1.15
[2025-08-05 23:26:41] [INFO] 客户端IP: *************
[2025-08-05 23:26:41] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:26:41] [INFO] 页面类型: merchant
[2025-08-05 23:26:41] [INFO] 商户ID: '8034567958'
[2025-08-05 23:26:41] [INFO] 商品ID: ''
[2025-08-05 23:26:41] [INFO] 订单ID: ''
[2025-08-05 23:27:05] [INFO] === 新的访问开始 ===
[2025-08-05 23:27:05] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:27:05] [INFO] 请求方法: GET
[2025-08-05 23:27:05] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:27:05] [INFO] POST参数: []
[2025-08-05 23:27:05] [INFO] User-Agent: TelegramBot (like TwitterBot)
[2025-08-05 23:27:05] [INFO] 客户端IP: ************
[2025-08-05 23:27:05] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:27:05] [INFO] 页面类型: merchant
[2025-08-05 23:27:05] [INFO] 商户ID: '8034567958'
[2025-08-05 23:27:05] [INFO] 商品ID: ''
[2025-08-05 23:27:05] [INFO] 订单ID: ''
[2025-08-05 23:27:24] [INFO] === 新的访问开始 ===
[2025-08-05 23:27:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:27:24] [INFO] 请求方法: GET
[2025-08-05 23:27:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:27:24] [INFO] POST参数: []
[2025-08-05 23:27:24] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5.1 Mobile/21F90 Safari/604.1
[2025-08-05 23:27:24] [INFO] 客户端IP: **************
[2025-08-05 23:27:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:27:24] [INFO] 页面类型: merchant
[2025-08-05 23:27:24] [INFO] 商户ID: '8034567958'
[2025-08-05 23:27:24] [INFO] 商品ID: ''
[2025-08-05 23:27:24] [INFO] 订单ID: ''
[2025-08-05 23:27:27] [INFO] === 新的访问开始 ===
[2025-08-05 23:27:27] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:27:27] [INFO] 请求方法: GET
[2025-08-05 23:27:27] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:27:27] [INFO] POST参数: []
[2025-08-05 23:27:27] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5.1 Mobile/21F90 Safari/604.1
[2025-08-05 23:27:27] [INFO] 客户端IP: **************
[2025-08-05 23:27:27] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:27:27] [INFO] 页面类型: merchant
[2025-08-05 23:27:27] [INFO] 商户ID: '8034567958'
[2025-08-05 23:27:27] [INFO] 商品ID: ''
[2025-08-05 23:27:27] [INFO] 订单ID: ''
[2025-08-05 23:53:20] [INFO] === 新的访问开始 ===
[2025-08-05 23:53:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:53:20] [INFO] 请求方法: GET
[2025-08-05 23:53:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:53:20] [INFO] POST参数: []
[2025-08-05 23:53:20] [INFO] User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/138 Version/11.1.1 Safari/605.1.15
[2025-08-05 23:53:20] [INFO] 客户端IP: **************
[2025-08-05 23:53:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:53:20] [INFO] 页面类型: merchant
[2025-08-05 23:53:20] [INFO] 商户ID: '8034567958'
[2025-08-05 23:53:20] [INFO] 商品ID: ''
[2025-08-05 23:53:20] [INFO] 订单ID: ''
[2025-08-05 23:54:10] [INFO] === 新的访问开始 ===
[2025-08-05 23:54:10] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:54:10] [INFO] 请求方法: GET
[2025-08-05 23:54:10] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:54:10] [INFO] POST参数: []
[2025-08-05 23:54:10] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-05 23:54:10] [INFO] 客户端IP: ************
[2025-08-05 23:54:10] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:54:10] [INFO] 页面类型: merchant
[2025-08-05 23:54:10] [INFO] 商户ID: '8034567958'
[2025-08-05 23:54:10] [INFO] 商品ID: ''
[2025-08-05 23:54:10] [INFO] 订单ID: ''
[2025-08-05 23:54:19] [INFO] === 新的访问开始 ===
[2025-08-05 23:54:19] [INFO] 请求URI: /shop.php?dd=https%3A%2F%2Fcloudshop.qnm6.top%2Fshop.php%3Fsj%3D8034567958
[2025-08-05 23:54:19] [INFO] 请求方法: GET
[2025-08-05 23:54:19] [INFO] GET参数: {"dd":"https:\/\/cloudshop.qnm6.top\/shop.php?sj=8034567958"}
[2025-08-05 23:54:19] [INFO] POST参数: []
[2025-08-05 23:54:19] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-05 23:54:19] [INFO] 客户端IP: ************
[2025-08-05 23:54:19] [INFO] 解析参数 - sj: '', sp: '', dd: 'https://cloudshop.qnm6.top/shop.php?sj=8034567958'
[2025-08-05 23:54:19] [INFO] 页面类型: order
[2025-08-05 23:54:19] [INFO] 商户ID: ''
[2025-08-05 23:54:19] [INFO] 商品ID: ''
[2025-08-05 23:54:19] [INFO] 订单ID: 'https://cloudshop.qnm6.top/shop.php?sj=8034567958'
[2025-08-05 23:54:43] [INFO] === 新的访问开始 ===
[2025-08-05 23:54:43] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-05 23:54:43] [INFO] 请求方法: GET
[2025-08-05 23:54:43] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-05 23:54:43] [INFO] POST参数: []
[2025-08-05 23:54:43] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-05 23:54:43] [INFO] 客户端IP: ************
[2025-08-05 23:54:43] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-05 23:54:43] [INFO] 页面类型: order
[2025-08-05 23:54:43] [INFO] 商户ID: ''
[2025-08-05 23:54:43] [INFO] 商品ID: ''
[2025-08-05 23:54:43] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-05 23:55:20] [INFO] === 新的访问开始 ===
[2025-08-05 23:55:20] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-05 23:55:20] [INFO] 请求方法: GET
[2025-08-05 23:55:20] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-05 23:55:20] [INFO] POST参数: []
[2025-08-05 23:55:20] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-05 23:55:20] [INFO] 客户端IP: ************
[2025-08-05 23:55:20] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-05 23:55:20] [INFO] 页面类型: order
[2025-08-05 23:55:20] [INFO] 商户ID: ''
[2025-08-05 23:55:20] [INFO] 商品ID: ''
[2025-08-05 23:55:20] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-05 23:55:56] [INFO] === 新的访问开始 ===
[2025-08-05 23:55:56] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:55:56] [INFO] 请求方法: GET
[2025-08-05 23:55:56] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:55:56] [INFO] POST参数: []
[2025-08-05 23:55:56] [INFO] User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_0) AppleWebKit/601.2.7 (KHTML, like Gecko) Version/9.0.1 Safari/601.2.7
[2025-08-05 23:55:56] [INFO] 客户端IP: ************
[2025-08-05 23:55:56] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:55:56] [INFO] 页面类型: merchant
[2025-08-05 23:55:56] [INFO] 商户ID: '8034567958'
[2025-08-05 23:55:56] [INFO] 商品ID: ''
[2025-08-05 23:55:56] [INFO] 订单ID: ''
[2025-08-05 23:56:11] [INFO] === 新的访问开始 ===
[2025-08-05 23:56:11] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-05 23:56:11] [INFO] 请求方法: GET
[2025-08-05 23:56:11] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-05 23:56:11] [INFO] POST参数: []
[2025-08-05 23:56:11] [INFO] User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_0) AppleWebKit/601.2.7 (KHTML, like Gecko) Version/9.0.1 Safari/601.2.7
[2025-08-05 23:56:11] [INFO] 客户端IP: ************
[2025-08-05 23:56:11] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-05 23:56:11] [INFO] 页面类型: order
[2025-08-05 23:56:11] [INFO] 商户ID: ''
[2025-08-05 23:56:11] [INFO] 商品ID: ''
[2025-08-05 23:56:11] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-05 23:57:36] [INFO] === 新的访问开始 ===
[2025-08-05 23:57:36] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-05 23:57:36] [INFO] 请求方法: GET
[2025-08-05 23:57:36] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-05 23:57:36] [INFO] POST参数: []
[2025-08-05 23:57:36] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-05 23:57:36] [INFO] 客户端IP: **************
[2025-08-05 23:57:36] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-05 23:57:36] [INFO] 页面类型: merchant
[2025-08-05 23:57:36] [INFO] 商户ID: '8034567958'
[2025-08-05 23:57:36] [INFO] 商品ID: ''
[2025-08-05 23:57:36] [INFO] 订单ID: ''
[2025-08-05 23:57:40] [INFO] === 新的访问开始 ===
[2025-08-05 23:57:40] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-05 23:57:40] [INFO] 请求方法: GET
[2025-08-05 23:57:40] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-05 23:57:40] [INFO] POST参数: []
[2025-08-05 23:57:40] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-05 23:57:40] [INFO] 客户端IP: **************
[2025-08-05 23:57:40] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-05 23:57:40] [INFO] 页面类型: order
[2025-08-05 23:57:40] [INFO] 商户ID: ''
[2025-08-05 23:57:40] [INFO] 商品ID: ''
[2025-08-05 23:57:40] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-06 00:00:17] [INFO] === 新的访问开始 ===
[2025-08-06 00:00:17] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-06 00:00:17] [INFO] 请求方法: GET
[2025-08-06 00:00:17] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-06 00:00:17] [INFO] POST参数: []
[2025-08-06 00:00:17] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-06 00:00:17] [INFO] 客户端IP: *************
[2025-08-06 00:00:17] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-06 00:00:17] [INFO] 页面类型: order
[2025-08-06 00:00:17] [INFO] 商户ID: ''
[2025-08-06 00:00:17] [INFO] 商品ID: ''
[2025-08-06 00:00:17] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-06 01:55:47] [INFO] === 新的访问开始 ===
[2025-08-06 01:55:47] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 01:55:47] [INFO] 请求方法: GET
[2025-08-06 01:55:47] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 01:55:47] [INFO] POST参数: []
[2025-08-06 01:55:47] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 01:55:47] [INFO] 客户端IP: *************
[2025-08-06 01:55:47] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 01:55:47] [INFO] 页面类型: merchant
[2025-08-06 01:55:47] [INFO] 商户ID: '8034567958'
[2025-08-06 01:55:47] [INFO] 商品ID: ''
[2025-08-06 01:55:47] [INFO] 订单ID: ''
[2025-08-06 02:16:01] [INFO] === 新的访问开始 ===
[2025-08-06 02:16:01] [INFO] 请求URI: /shop.php?sp=23
[2025-08-06 02:16:01] [INFO] 请求方法: GET
[2025-08-06 02:16:01] [INFO] GET参数: {"sp":"23"}
[2025-08-06 02:16:01] [INFO] POST参数: []
[2025-08-06 02:16:01] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:16:01] [INFO] 客户端IP: *************
[2025-08-06 02:16:01] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-06 02:16:01] [INFO] 页面类型: product
[2025-08-06 02:16:01] [INFO] 商户ID: ''
[2025-08-06 02:16:01] [INFO] 商品ID: '23'
[2025-08-06 02:16:01] [INFO] 订单ID: ''
[2025-08-06 02:17:24] [INFO] === 新的访问开始 ===
[2025-08-06 02:17:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:17:24] [INFO] 请求方法: GET
[2025-08-06 02:17:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:17:24] [INFO] POST参数: []
[2025-08-06 02:17:24] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:17:24] [INFO] 客户端IP: *************
[2025-08-06 02:17:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:17:24] [INFO] 页面类型: merchant
[2025-08-06 02:17:24] [INFO] 商户ID: '8034567958'
[2025-08-06 02:17:24] [INFO] 商品ID: ''
[2025-08-06 02:17:24] [INFO] 订单ID: ''
[2025-08-06 02:17:36] [INFO] === 新的访问开始 ===
[2025-08-06 02:17:36] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:17:36] [INFO] 请求方法: GET
[2025-08-06 02:17:36] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:17:36] [INFO] POST参数: []
[2025-08-06 02:17:36] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:17:36] [INFO] 客户端IP: *************
[2025-08-06 02:17:36] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:17:36] [INFO] 页面类型: merchant
[2025-08-06 02:17:36] [INFO] 商户ID: '8034567958'
[2025-08-06 02:17:36] [INFO] 商品ID: ''
[2025-08-06 02:17:36] [INFO] 订单ID: ''
[2025-08-06 02:20:28] [INFO] === 新的访问开始 ===
[2025-08-06 02:20:28] [INFO] 请求URI: /shop.php?sp=23
[2025-08-06 02:20:28] [INFO] 请求方法: GET
[2025-08-06 02:20:28] [INFO] GET参数: {"sp":"23"}
[2025-08-06 02:20:28] [INFO] POST参数: []
[2025-08-06 02:20:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:20:28] [INFO] 客户端IP: *************
[2025-08-06 02:20:28] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-06 02:20:28] [INFO] 页面类型: product
[2025-08-06 02:20:28] [INFO] 商户ID: ''
[2025-08-06 02:20:28] [INFO] 商品ID: '23'
[2025-08-06 02:20:28] [INFO] 订单ID: ''
[2025-08-06 02:20:44] [INFO] === 新的访问开始 ===
[2025-08-06 02:20:44] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:20:44] [INFO] 请求方法: GET
[2025-08-06 02:20:44] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:20:44] [INFO] POST参数: []
[2025-08-06 02:20:44] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:20:44] [INFO] 客户端IP: *************
[2025-08-06 02:20:44] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:20:44] [INFO] 页面类型: merchant
[2025-08-06 02:20:44] [INFO] 商户ID: '8034567958'
[2025-08-06 02:20:44] [INFO] 商品ID: ''
[2025-08-06 02:20:44] [INFO] 订单ID: ''
[2025-08-06 02:23:56] [INFO] === 新的访问开始 ===
[2025-08-06 02:23:56] [INFO] 请求URI: /shop.php?dd=jbE4R6z1
[2025-08-06 02:23:56] [INFO] 请求方法: GET
[2025-08-06 02:23:56] [INFO] GET参数: {"dd":"jbE4R6z1"}
[2025-08-06 02:23:56] [INFO] POST参数: []
[2025-08-06 02:23:56] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:23:56] [INFO] 客户端IP: **************
[2025-08-06 02:23:56] [INFO] 解析参数 - sj: '', sp: '', dd: 'jbE4R6z1'
[2025-08-06 02:23:56] [INFO] 页面类型: order
[2025-08-06 02:23:56] [INFO] 商户ID: ''
[2025-08-06 02:23:56] [INFO] 商品ID: ''
[2025-08-06 02:23:56] [INFO] 订单ID: 'jbE4R6z1'
[2025-08-06 02:24:18] [INFO] === 新的访问开始 ===
[2025-08-06 02:24:18] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:24:18] [INFO] 请求方法: GET
[2025-08-06 02:24:18] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:24:18] [INFO] POST参数: []
[2025-08-06 02:24:18] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:24:18] [INFO] 客户端IP: **************
[2025-08-06 02:24:18] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:24:18] [INFO] 页面类型: merchant
[2025-08-06 02:24:18] [INFO] 商户ID: '8034567958'
[2025-08-06 02:24:18] [INFO] 商品ID: ''
[2025-08-06 02:24:18] [INFO] 订单ID: ''
[2025-08-06 02:36:21] [INFO] === 新的访问开始 ===
[2025-08-06 02:36:21] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:36:21] [INFO] 请求方法: GET
[2025-08-06 02:36:21] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:36:21] [INFO] POST参数: []
[2025-08-06 02:36:21] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:36:21] [INFO] 客户端IP: ************
[2025-08-06 02:36:21] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:36:21] [INFO] 页面类型: merchant
[2025-08-06 02:36:21] [INFO] 商户ID: '8034567958'
[2025-08-06 02:36:21] [INFO] 商品ID: ''
[2025-08-06 02:36:21] [INFO] 订单ID: ''
[2025-08-06 02:44:39] [INFO] === 新的访问开始 ===
[2025-08-06 02:44:39] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:44:39] [INFO] 请求方法: GET
[2025-08-06 02:44:39] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:44:39] [INFO] POST参数: []
[2025-08-06 02:44:39] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 02:44:39] [INFO] 客户端IP: **************
[2025-08-06 02:44:39] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:44:39] [INFO] 页面类型: merchant
[2025-08-06 02:44:39] [INFO] 商户ID: '8034567958'
[2025-08-06 02:44:39] [INFO] 商品ID: ''
[2025-08-06 02:44:39] [INFO] 订单ID: ''
[2025-08-06 02:47:42] [INFO] === 新的访问开始 ===
[2025-08-06 02:47:42] [INFO] 请求URI: /shop.php?dd=ORDER17544180458096
[2025-08-06 02:47:42] [INFO] 请求方法: GET
[2025-08-06 02:47:42] [INFO] GET参数: {"dd":"ORDER17544180458096"}
[2025-08-06 02:47:42] [INFO] POST参数: []
[2025-08-06 02:47:42] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 02:47:42] [INFO] 客户端IP: **************
[2025-08-06 02:47:42] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544180458096'
[2025-08-06 02:47:42] [INFO] 页面类型: order
[2025-08-06 02:47:42] [INFO] 商户ID: ''
[2025-08-06 02:47:42] [INFO] 商品ID: ''
[2025-08-06 02:47:42] [INFO] 订单ID: 'ORDER17544180458096'
[2025-08-06 02:49:54] [INFO] === 新的访问开始 ===
[2025-08-06 02:49:54] [INFO] 请求URI: /shop.php?dd=ORDER17544180458096
[2025-08-06 02:49:54] [INFO] 请求方法: GET
[2025-08-06 02:49:54] [INFO] GET参数: {"dd":"ORDER17544180458096"}
[2025-08-06 02:49:54] [INFO] POST参数: []
[2025-08-06 02:49:54] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 02:49:54] [INFO] 客户端IP: **************
[2025-08-06 02:49:54] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544180458096'
[2025-08-06 02:49:54] [INFO] 页面类型: order
[2025-08-06 02:49:54] [INFO] 商户ID: ''
[2025-08-06 02:49:54] [INFO] 商品ID: ''
[2025-08-06 02:49:54] [INFO] 订单ID: 'ORDER17544180458096'
[2025-08-06 02:54:00] [INFO] === 新的访问开始 ===
[2025-08-06 02:54:00] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:54:00] [INFO] 请求方法: GET
[2025-08-06 02:54:00] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:54:00] [INFO] POST参数: []
[2025-08-06 02:54:00] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:54:00] [INFO] 客户端IP: **************
[2025-08-06 02:54:00] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:54:00] [INFO] 页面类型: merchant
[2025-08-06 02:54:00] [INFO] 商户ID: '8034567958'
[2025-08-06 02:54:00] [INFO] 商品ID: ''
[2025-08-06 02:54:00] [INFO] 订单ID: ''
[2025-08-06 02:54:20] [INFO] === 新的访问开始 ===
[2025-08-06 02:54:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:54:20] [INFO] 请求方法: GET
[2025-08-06 02:54:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:54:20] [INFO] POST参数: []
[2025-08-06 02:54:20] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:54:20] [INFO] 客户端IP: **************
[2025-08-06 02:54:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:54:20] [INFO] 页面类型: merchant
[2025-08-06 02:54:20] [INFO] 商户ID: '8034567958'
[2025-08-06 02:54:20] [INFO] 商品ID: ''
[2025-08-06 02:54:20] [INFO] 订单ID: ''
[2025-08-06 02:54:28] [INFO] === 新的访问开始 ===
[2025-08-06 02:54:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 02:54:28] [INFO] 请求方法: GET
[2025-08-06 02:54:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 02:54:28] [INFO] POST参数: []
[2025-08-06 02:54:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 02:54:28] [INFO] 客户端IP: **************
[2025-08-06 02:54:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 02:54:28] [INFO] 页面类型: merchant
[2025-08-06 02:54:28] [INFO] 商户ID: '8034567958'
[2025-08-06 02:54:28] [INFO] 商品ID: ''
[2025-08-06 02:54:28] [INFO] 订单ID: ''
[2025-08-06 03:44:43] [INFO] === 新的访问开始 ===
[2025-08-06 03:44:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 03:44:43] [INFO] 请求方法: GET
[2025-08-06 03:44:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 03:44:43] [INFO] POST参数: []
[2025-08-06 03:44:43] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:44:43] [INFO] 客户端IP: **************
[2025-08-06 03:44:43] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 03:44:43] [INFO] 页面类型: merchant
[2025-08-06 03:44:43] [INFO] 商户ID: '8034567958'
[2025-08-06 03:44:43] [INFO] 商品ID: ''
[2025-08-06 03:44:43] [INFO] 订单ID: ''
[2025-08-06 03:45:32] [INFO] === 新的访问开始 ===
[2025-08-06 03:45:32] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 03:45:32] [INFO] 请求方法: GET
[2025-08-06 03:45:32] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 03:45:32] [INFO] POST参数: []
[2025-08-06 03:45:32] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:45:32] [INFO] 客户端IP: **************
[2025-08-06 03:45:32] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 03:45:32] [INFO] 页面类型: merchant
[2025-08-06 03:45:32] [INFO] 商户ID: '8034567958'
[2025-08-06 03:45:32] [INFO] 商品ID: ''
[2025-08-06 03:45:32] [INFO] 订单ID: ''
[2025-08-06 03:47:40] [INFO] === 新的访问开始 ===
[2025-08-06 03:47:40] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 03:47:40] [INFO] 请求方法: GET
[2025-08-06 03:47:40] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 03:47:40] [INFO] POST参数: []
[2025-08-06 03:47:40] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:47:40] [INFO] 客户端IP: **************
[2025-08-06 03:47:40] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 03:47:40] [INFO] 页面类型: merchant
[2025-08-06 03:47:40] [INFO] 商户ID: '8034567958'
[2025-08-06 03:47:40] [INFO] 商品ID: ''
[2025-08-06 03:47:40] [INFO] 订单ID: ''
[2025-08-06 03:49:57] [INFO] === 新的访问开始 ===
[2025-08-06 03:49:57] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 03:49:57] [INFO] 请求方法: GET
[2025-08-06 03:49:57] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 03:49:57] [INFO] POST参数: []
[2025-08-06 03:49:57] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:49:57] [INFO] 客户端IP: **************
[2025-08-06 03:49:57] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 03:49:57] [INFO] 页面类型: merchant
[2025-08-06 03:49:57] [INFO] 商户ID: '8034567958'
[2025-08-06 03:49:57] [INFO] 商品ID: ''
[2025-08-06 03:49:57] [INFO] 订单ID: ''
[2025-08-06 03:50:02] [INFO] === 新的访问开始 ===
[2025-08-06 03:50:02] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 03:50:02] [INFO] 请求方法: GET
[2025-08-06 03:50:02] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 03:50:02] [INFO] POST参数: []
[2025-08-06 03:50:02] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:50:02] [INFO] 客户端IP: **************
[2025-08-06 03:50:02] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 03:50:02] [INFO] 页面类型: order
[2025-08-06 03:50:02] [INFO] 商户ID: ''
[2025-08-06 03:50:02] [INFO] 商品ID: ''
[2025-08-06 03:50:02] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 03:50:18] [INFO] === 新的访问开始 ===
[2025-08-06 03:50:18] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 03:50:18] [INFO] 请求方法: GET
[2025-08-06 03:50:18] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 03:50:18] [INFO] POST参数: []
[2025-08-06 03:50:18] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:50:18] [INFO] 客户端IP: **************
[2025-08-06 03:50:18] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 03:50:18] [INFO] 页面类型: order
[2025-08-06 03:50:18] [INFO] 商户ID: ''
[2025-08-06 03:50:18] [INFO] 商品ID: ''
[2025-08-06 03:50:18] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 03:57:44] [INFO] === 新的访问开始 ===
[2025-08-06 03:57:44] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 03:57:44] [INFO] 请求方法: GET
[2025-08-06 03:57:44] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 03:57:44] [INFO] POST参数: []
[2025-08-06 03:57:44] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:57:44] [INFO] 客户端IP: **************
[2025-08-06 03:57:44] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 03:57:44] [INFO] 页面类型: order
[2025-08-06 03:57:44] [INFO] 商户ID: ''
[2025-08-06 03:57:44] [INFO] 商品ID: ''
[2025-08-06 03:57:44] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 03:57:58] [INFO] === 新的访问开始 ===
[2025-08-06 03:57:58] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 03:57:58] [INFO] 请求方法: GET
[2025-08-06 03:57:58] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 03:57:58] [INFO] POST参数: []
[2025-08-06 03:57:58] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:57:58] [INFO] 客户端IP: **************
[2025-08-06 03:57:58] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 03:57:58] [INFO] 页面类型: merchant
[2025-08-06 03:57:58] [INFO] 商户ID: '8034567958'
[2025-08-06 03:57:58] [INFO] 商品ID: ''
[2025-08-06 03:57:58] [INFO] 订单ID: ''
[2025-08-06 03:58:02] [INFO] === 新的访问开始 ===
[2025-08-06 03:58:02] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 03:58:02] [INFO] 请求方法: GET
[2025-08-06 03:58:02] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 03:58:02] [INFO] POST参数: []
[2025-08-06 03:58:02] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:58:02] [INFO] 客户端IP: **************
[2025-08-06 03:58:02] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 03:58:02] [INFO] 页面类型: merchant
[2025-08-06 03:58:02] [INFO] 商户ID: '8034567958'
[2025-08-06 03:58:02] [INFO] 商品ID: ''
[2025-08-06 03:58:02] [INFO] 订单ID: ''
[2025-08-06 03:59:43] [INFO] === 新的访问开始 ===
[2025-08-06 03:59:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 03:59:43] [INFO] 请求方法: GET
[2025-08-06 03:59:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 03:59:43] [INFO] POST参数: []
[2025-08-06 03:59:43] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:59:43] [INFO] 客户端IP: **************
[2025-08-06 03:59:43] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 03:59:43] [INFO] 页面类型: merchant
[2025-08-06 03:59:43] [INFO] 商户ID: '8034567958'
[2025-08-06 03:59:43] [INFO] 商品ID: ''
[2025-08-06 03:59:43] [INFO] 订单ID: ''
[2025-08-06 03:59:51] [INFO] === 新的访问开始 ===
[2025-08-06 03:59:51] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 03:59:51] [INFO] 请求方法: GET
[2025-08-06 03:59:51] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 03:59:51] [INFO] POST参数: []
[2025-08-06 03:59:51] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 03:59:51] [INFO] 客户端IP: **************
[2025-08-06 03:59:51] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 03:59:51] [INFO] 页面类型: order
[2025-08-06 03:59:51] [INFO] 商户ID: ''
[2025-08-06 03:59:51] [INFO] 商品ID: ''
[2025-08-06 03:59:51] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:06:27] [INFO] === 新的访问开始 ===
[2025-08-06 04:06:27] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 04:06:27] [INFO] 请求方法: GET
[2025-08-06 04:06:27] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 04:06:27] [INFO] POST参数: []
[2025-08-06 04:06:27] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:06:27] [INFO] 客户端IP: **************
[2025-08-06 04:06:27] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 04:06:27] [INFO] 页面类型: order
[2025-08-06 04:06:27] [INFO] 商户ID: ''
[2025-08-06 04:06:27] [INFO] 商品ID: ''
[2025-08-06 04:06:27] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:06:37] [INFO] === 新的访问开始 ===
[2025-08-06 04:06:37] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 04:06:37] [INFO] 请求方法: GET
[2025-08-06 04:06:37] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 04:06:37] [INFO] POST参数: []
[2025-08-06 04:06:37] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:06:37] [INFO] 客户端IP: **************
[2025-08-06 04:06:37] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 04:06:37] [INFO] 页面类型: order
[2025-08-06 04:06:37] [INFO] 商户ID: ''
[2025-08-06 04:06:37] [INFO] 商品ID: ''
[2025-08-06 04:06:37] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:10:08] [INFO] === 新的访问开始 ===
[2025-08-06 04:10:08] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 04:10:08] [INFO] 请求方法: GET
[2025-08-06 04:10:08] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 04:10:08] [INFO] POST参数: []
[2025-08-06 04:10:08] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:10:08] [INFO] 客户端IP: ************
[2025-08-06 04:10:08] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 04:10:08] [INFO] 页面类型: order
[2025-08-06 04:10:08] [INFO] 商户ID: ''
[2025-08-06 04:10:08] [INFO] 商品ID: ''
[2025-08-06 04:10:08] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:10:29] [INFO] === 新的访问开始 ===
[2025-08-06 04:10:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:10:29] [INFO] 请求方法: GET
[2025-08-06 04:10:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:10:29] [INFO] POST参数: []
[2025-08-06 04:10:29] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:10:29] [INFO] 客户端IP: ************
[2025-08-06 04:10:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:10:29] [INFO] 页面类型: merchant
[2025-08-06 04:10:29] [INFO] 商户ID: '8034567958'
[2025-08-06 04:10:29] [INFO] 商品ID: ''
[2025-08-06 04:10:29] [INFO] 订单ID: ''
[2025-08-06 04:10:42] [INFO] === 新的访问开始 ===
[2025-08-06 04:10:42] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 04:10:42] [INFO] 请求方法: GET
[2025-08-06 04:10:42] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 04:10:42] [INFO] POST参数: []
[2025-08-06 04:10:42] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:10:42] [INFO] 客户端IP: ************
[2025-08-06 04:10:42] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 04:10:42] [INFO] 页面类型: order
[2025-08-06 04:10:42] [INFO] 商户ID: ''
[2025-08-06 04:10:42] [INFO] 商品ID: ''
[2025-08-06 04:10:42] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:19:48] [INFO] === 新的访问开始 ===
[2025-08-06 04:19:48] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:19:48] [INFO] 请求方法: GET
[2025-08-06 04:19:48] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:19:48] [INFO] POST参数: []
[2025-08-06 04:19:48] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.71 MQQBrowser/19.2 Mobile Safari/537.36 COVC/048201
[2025-08-06 04:19:48] [INFO] 客户端IP: **************
[2025-08-06 04:19:48] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:19:48] [INFO] 页面类型: merchant
[2025-08-06 04:19:48] [INFO] 商户ID: '8034567958'
[2025-08-06 04:19:48] [INFO] 商品ID: ''
[2025-08-06 04:19:48] [INFO] 订单ID: ''
[2025-08-06 04:19:55] [INFO] === 新的访问开始 ===
[2025-08-06 04:19:55] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 04:19:55] [INFO] 请求方法: GET
[2025-08-06 04:19:55] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 04:19:55] [INFO] POST参数: []
[2025-08-06 04:19:55] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.71 MQQBrowser/19.2 Mobile Safari/537.36 COVC/048201
[2025-08-06 04:19:55] [INFO] 客户端IP: **************
[2025-08-06 04:19:55] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 04:19:55] [INFO] 页面类型: order
[2025-08-06 04:19:55] [INFO] 商户ID: ''
[2025-08-06 04:19:55] [INFO] 商品ID: ''
[2025-08-06 04:19:55] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:22:09] [INFO] === 新的访问开始 ===
[2025-08-06 04:22:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:22:09] [INFO] 请求方法: GET
[2025-08-06 04:22:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:22:09] [INFO] POST参数: []
[2025-08-06 04:22:09] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/121.0.6167.71 MQQBrowser/19.2 Mobile Safari/537.36 COVC/048201
[2025-08-06 04:22:09] [INFO] 客户端IP: **************
[2025-08-06 04:22:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:22:09] [INFO] 页面类型: merchant
[2025-08-06 04:22:09] [INFO] 商户ID: '8034567958'
[2025-08-06 04:22:09] [INFO] 商品ID: ''
[2025-08-06 04:22:09] [INFO] 订单ID: ''
[2025-08-06 04:27:51] [INFO] === 新的访问开始 ===
[2025-08-06 04:27:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:27:51] [INFO] 请求方法: GET
[2025-08-06 04:27:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:27:51] [INFO] POST参数: []
[2025-08-06 04:27:51] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:27:51] [INFO] 客户端IP: **************
[2025-08-06 04:27:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:27:51] [INFO] 页面类型: merchant
[2025-08-06 04:27:51] [INFO] 商户ID: '8034567958'
[2025-08-06 04:27:51] [INFO] 商品ID: ''
[2025-08-06 04:27:51] [INFO] 订单ID: ''
[2025-08-06 04:27:56] [INFO] === 新的访问开始 ===
[2025-08-06 04:27:56] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 04:27:56] [INFO] 请求方法: GET
[2025-08-06 04:27:56] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 04:27:56] [INFO] POST参数: []
[2025-08-06 04:27:56] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:27:56] [INFO] 客户端IP: **************
[2025-08-06 04:27:56] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 04:27:56] [INFO] 页面类型: order
[2025-08-06 04:27:56] [INFO] 商户ID: ''
[2025-08-06 04:27:56] [INFO] 商品ID: ''
[2025-08-06 04:27:56] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:32:07] [INFO] === 新的访问开始 ===
[2025-08-06 04:32:07] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:32:07] [INFO] 请求方法: GET
[2025-08-06 04:32:07] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:32:07] [INFO] POST参数: []
[2025-08-06 04:32:07] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:32:07] [INFO] 客户端IP: **************
[2025-08-06 04:32:07] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:32:07] [INFO] 页面类型: merchant
[2025-08-06 04:32:07] [INFO] 商户ID: '8034567958'
[2025-08-06 04:32:07] [INFO] 商品ID: ''
[2025-08-06 04:32:07] [INFO] 订单ID: ''
[2025-08-06 04:32:12] [INFO] === 新的访问开始 ===
[2025-08-06 04:32:12] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 04:32:12] [INFO] 请求方法: GET
[2025-08-06 04:32:12] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 04:32:12] [INFO] POST参数: []
[2025-08-06 04:32:12] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 04:32:12] [INFO] 客户端IP: **************
[2025-08-06 04:32:12] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 04:32:12] [INFO] 页面类型: order
[2025-08-06 04:32:12] [INFO] 商户ID: ''
[2025-08-06 04:32:12] [INFO] 商品ID: ''
[2025-08-06 04:32:12] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 04:32:39] [INFO] === 新的访问开始 ===
[2025-08-06 04:32:39] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:32:39] [INFO] 请求方法: GET
[2025-08-06 04:32:39] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:32:39] [INFO] POST参数: []
[2025-08-06 04:32:39] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 04:32:39] [INFO] 客户端IP: **************
[2025-08-06 04:32:39] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:32:39] [INFO] 页面类型: merchant
[2025-08-06 04:32:39] [INFO] 商户ID: '8034567958'
[2025-08-06 04:32:39] [INFO] 商品ID: ''
[2025-08-06 04:32:39] [INFO] 订单ID: ''
[2025-08-06 04:32:45] [INFO] === 新的访问开始 ===
[2025-08-06 04:32:45] [INFO] 请求URI: /shop.php?dd=ORDER17544180458096
[2025-08-06 04:32:45] [INFO] 请求方法: GET
[2025-08-06 04:32:45] [INFO] GET参数: {"dd":"ORDER17544180458096"}
[2025-08-06 04:32:45] [INFO] POST参数: []
[2025-08-06 04:32:45] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 04:32:45] [INFO] 客户端IP: **************
[2025-08-06 04:32:45] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544180458096'
[2025-08-06 04:32:45] [INFO] 页面类型: order
[2025-08-06 04:32:45] [INFO] 商户ID: ''
[2025-08-06 04:32:45] [INFO] 商品ID: ''
[2025-08-06 04:32:45] [INFO] 订单ID: 'ORDER17544180458096'
[2025-08-06 04:44:37] [INFO] === 新的访问开始 ===
[2025-08-06 04:44:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:44:37] [INFO] 请求方法: GET
[2025-08-06 04:44:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:44:37] [INFO] POST参数: []
[2025-08-06 04:44:37] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 04:44:37] [INFO] 客户端IP: ************
[2025-08-06 04:44:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:44:37] [INFO] 页面类型: merchant
[2025-08-06 04:44:37] [INFO] 商户ID: '8034567958'
[2025-08-06 04:44:37] [INFO] 商品ID: ''
[2025-08-06 04:44:37] [INFO] 订单ID: ''
[2025-08-06 04:54:32] [INFO] === 新的访问开始 ===
[2025-08-06 04:54:32] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 04:54:32] [INFO] 请求方法: GET
[2025-08-06 04:54:32] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 04:54:32] [INFO] POST参数: []
[2025-08-06 04:54:32] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 04:54:32] [INFO] 客户端IP: *************
[2025-08-06 04:54:32] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 04:54:32] [INFO] 页面类型: merchant
[2025-08-06 04:54:32] [INFO] 商户ID: '8034567958'
[2025-08-06 04:54:32] [INFO] 商品ID: ''
[2025-08-06 04:54:32] [INFO] 订单ID: ''
[2025-08-06 04:54:54] [INFO] === 新的访问开始 ===
[2025-08-06 04:54:54] [INFO] 请求URI: /shop.php?dd=ORDER17544180458096
[2025-08-06 04:54:54] [INFO] 请求方法: GET
[2025-08-06 04:54:54] [INFO] GET参数: {"dd":"ORDER17544180458096"}
[2025-08-06 04:54:54] [INFO] POST参数: []
[2025-08-06 04:54:54] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 04:54:54] [INFO] 客户端IP: *************
[2025-08-06 04:54:54] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544180458096'
[2025-08-06 04:54:54] [INFO] 页面类型: order
[2025-08-06 04:54:54] [INFO] 商户ID: ''
[2025-08-06 04:54:54] [INFO] 商品ID: ''
[2025-08-06 04:54:54] [INFO] 订单ID: 'ORDER17544180458096'
[2025-08-06 06:18:04] [INFO] === 新的访问开始 ===
[2025-08-06 06:18:04] [INFO] 请求URI: /shop.php?dd=https%3A%2F%2Fcloudshop.qnm6.top%2Fshop.php%3Fsj%3D8034567958
[2025-08-06 06:18:04] [INFO] 请求方法: GET
[2025-08-06 06:18:04] [INFO] GET参数: {"dd":"https:\/\/cloudshop.qnm6.top\/shop.php?sj=8034567958"}
[2025-08-06 06:18:04] [INFO] POST参数: []
[2025-08-06 06:18:04] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-06 06:18:04] [INFO] 客户端IP: ************
[2025-08-06 06:18:04] [INFO] 解析参数 - sj: '', sp: '', dd: 'https://cloudshop.qnm6.top/shop.php?sj=8034567958'
[2025-08-06 06:18:04] [INFO] 页面类型: order
[2025-08-06 06:18:04] [INFO] 商户ID: ''
[2025-08-06 06:18:04] [INFO] 商品ID: ''
[2025-08-06 06:18:04] [INFO] 订单ID: 'https://cloudshop.qnm6.top/shop.php?sj=8034567958'
[2025-08-06 06:18:15] [INFO] === 新的访问开始 ===
[2025-08-06 06:18:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 06:18:15] [INFO] 请求方法: GET
[2025-08-06 06:18:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 06:18:15] [INFO] POST参数: []
[2025-08-06 06:18:15] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-06 06:18:15] [INFO] 客户端IP: ************
[2025-08-06 06:18:15] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 06:18:15] [INFO] 页面类型: merchant
[2025-08-06 06:18:15] [INFO] 商户ID: '8034567958'
[2025-08-06 06:18:15] [INFO] 商品ID: ''
[2025-08-06 06:18:15] [INFO] 订单ID: ''
[2025-08-06 06:18:44] [INFO] === 新的访问开始 ===
[2025-08-06 06:18:44] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-06 06:18:44] [INFO] 请求方法: GET
[2025-08-06 06:18:44] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-06 06:18:44] [INFO] POST参数: []
[2025-08-06 06:18:44] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-06 06:18:44] [INFO] 客户端IP: ************
[2025-08-06 06:18:44] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-06 06:18:44] [INFO] 页面类型: order
[2025-08-06 06:18:44] [INFO] 商户ID: ''
[2025-08-06 06:18:44] [INFO] 商品ID: ''
[2025-08-06 06:18:44] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-06 06:47:28] [INFO] === 新的访问开始 ===
[2025-08-06 06:47:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 06:47:28] [INFO] 请求方法: GET
[2025-08-06 06:47:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 06:47:28] [INFO] POST参数: []
[2025-08-06 06:47:28] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-06 06:47:28] [INFO] 客户端IP: *************
[2025-08-06 06:47:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 06:47:28] [INFO] 页面类型: merchant
[2025-08-06 06:47:28] [INFO] 商户ID: '8034567958'
[2025-08-06 06:47:28] [INFO] 商品ID: ''
[2025-08-06 06:47:28] [INFO] 订单ID: ''
[2025-08-06 06:47:45] [INFO] === 新的访问开始 ===
[2025-08-06 06:47:45] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-06 06:47:45] [INFO] 请求方法: GET
[2025-08-06 06:47:45] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-06 06:47:45] [INFO] POST参数: []
[2025-08-06 06:47:45] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-06 06:47:45] [INFO] 客户端IP: **************
[2025-08-06 06:47:45] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-06 06:47:45] [INFO] 页面类型: order
[2025-08-06 06:47:45] [INFO] 商户ID: ''
[2025-08-06 06:47:45] [INFO] 商品ID: ''
[2025-08-06 06:47:45] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-06 06:48:06] [INFO] === 新的访问开始 ===
[2025-08-06 06:48:06] [INFO] 请求URI: /shop.php?dd=ORDER17543709712422
[2025-08-06 06:48:06] [INFO] 请求方法: GET
[2025-08-06 06:48:06] [INFO] GET参数: {"dd":"ORDER17543709712422"}
[2025-08-06 06:48:06] [INFO] POST参数: []
[2025-08-06 06:48:06] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-06 06:48:06] [INFO] 客户端IP: *************
[2025-08-06 06:48:06] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17543709712422'
[2025-08-06 06:48:06] [INFO] 页面类型: order
[2025-08-06 06:48:06] [INFO] 商户ID: ''
[2025-08-06 06:48:06] [INFO] 商品ID: ''
[2025-08-06 06:48:06] [INFO] 订单ID: 'ORDER17543709712422'
[2025-08-06 07:20:17] [INFO] === 新的访问开始 ===
[2025-08-06 07:20:17] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 07:20:17] [INFO] 请求方法: GET
[2025-08-06 07:20:17] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 07:20:17] [INFO] POST参数: []
[2025-08-06 07:20:17] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 07:20:17] [INFO] 客户端IP: ************
[2025-08-06 07:20:17] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 07:20:17] [INFO] 页面类型: merchant
[2025-08-06 07:20:17] [INFO] 商户ID: '8034567958'
[2025-08-06 07:20:17] [INFO] 商品ID: ''
[2025-08-06 07:20:17] [INFO] 订单ID: ''
[2025-08-06 07:20:23] [INFO] === 新的访问开始 ===
[2025-08-06 07:20:23] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 07:20:23] [INFO] 请求方法: GET
[2025-08-06 07:20:23] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 07:20:23] [INFO] POST参数: []
[2025-08-06 07:20:23] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 07:20:23] [INFO] 客户端IP: ************
[2025-08-06 07:20:23] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 07:20:23] [INFO] 页面类型: order
[2025-08-06 07:20:23] [INFO] 商户ID: ''
[2025-08-06 07:20:23] [INFO] 商品ID: ''
[2025-08-06 07:20:23] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 07:45:35] [INFO] === 新的访问开始 ===
[2025-08-06 07:45:35] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 07:45:35] [INFO] 请求方法: GET
[2025-08-06 07:45:35] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 07:45:35] [INFO] POST参数: []
[2025-08-06 07:45:35] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 07:45:35] [INFO] 客户端IP: **************
[2025-08-06 07:45:35] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 07:45:35] [INFO] 页面类型: merchant
[2025-08-06 07:45:35] [INFO] 商户ID: '8034567958'
[2025-08-06 07:45:35] [INFO] 商品ID: ''
[2025-08-06 07:45:35] [INFO] 订单ID: ''
[2025-08-06 07:45:41] [INFO] === 新的访问开始 ===
[2025-08-06 07:45:41] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 07:45:41] [INFO] 请求方法: GET
[2025-08-06 07:45:41] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 07:45:41] [INFO] POST参数: []
[2025-08-06 07:45:41] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 07:45:41] [INFO] 客户端IP: **************
[2025-08-06 07:45:41] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 07:45:41] [INFO] 页面类型: order
[2025-08-06 07:45:41] [INFO] 商户ID: ''
[2025-08-06 07:45:41] [INFO] 商品ID: ''
[2025-08-06 07:45:41] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 08:23:27] [INFO] === 新的访问开始 ===
[2025-08-06 08:23:27] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 08:23:27] [INFO] 请求方法: GET
[2025-08-06 08:23:27] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 08:23:27] [INFO] POST参数: []
[2025-08-06 08:23:27] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 08:23:27] [INFO] 客户端IP: **************
[2025-08-06 08:23:27] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 08:23:27] [INFO] 页面类型: merchant
[2025-08-06 08:23:27] [INFO] 商户ID: '8034567958'
[2025-08-06 08:23:27] [INFO] 商品ID: ''
[2025-08-06 08:23:27] [INFO] 订单ID: ''
[2025-08-06 08:41:32] [INFO] === 新的访问开始 ===
[2025-08-06 08:41:32] [INFO] 请求URI: /shop.php?sp=23
[2025-08-06 08:41:32] [INFO] 请求方法: GET
[2025-08-06 08:41:32] [INFO] GET参数: {"sp":"23"}
[2025-08-06 08:41:32] [INFO] POST参数: []
[2025-08-06 08:41:32] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 08:41:32] [INFO] 客户端IP: *************
[2025-08-06 08:41:32] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-06 08:41:32] [INFO] 页面类型: product
[2025-08-06 08:41:32] [INFO] 商户ID: ''
[2025-08-06 08:41:32] [INFO] 商品ID: '23'
[2025-08-06 08:41:32] [INFO] 订单ID: ''
[2025-08-06 09:03:08] [INFO] === 新的访问开始 ===
[2025-08-06 09:03:08] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:03:08] [INFO] 请求方法: GET
[2025-08-06 09:03:08] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:03:08] [INFO] POST参数: []
[2025-08-06 09:03:08] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:03:08] [INFO] 客户端IP: **************
[2025-08-06 09:03:08] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:03:08] [INFO] 页面类型: merchant
[2025-08-06 09:03:08] [INFO] 商户ID: '8034567958'
[2025-08-06 09:03:08] [INFO] 商品ID: ''
[2025-08-06 09:03:08] [INFO] 订单ID: ''
[2025-08-06 09:05:34] [INFO] === 新的访问开始 ===
[2025-08-06 09:05:34] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:05:34] [INFO] 请求方法: GET
[2025-08-06 09:05:34] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:05:34] [INFO] POST参数: []
[2025-08-06 09:05:34] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-06 09:05:34] [INFO] 客户端IP: **************
[2025-08-06 09:05:34] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:05:34] [INFO] 页面类型: merchant
[2025-08-06 09:05:34] [INFO] 商户ID: '8034567958'
[2025-08-06 09:05:34] [INFO] 商品ID: ''
[2025-08-06 09:05:34] [INFO] 订单ID: ''
[2025-08-06 09:05:35] [INFO] === 新的访问开始 ===
[2025-08-06 09:05:35] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:05:35] [INFO] 请求方法: GET
[2025-08-06 09:05:35] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:05:35] [INFO] POST参数: []
[2025-08-06 09:05:35] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-06 09:05:35] [INFO] 客户端IP: ************
[2025-08-06 09:05:35] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:05:35] [INFO] 页面类型: merchant
[2025-08-06 09:05:35] [INFO] 商户ID: '8034567958'
[2025-08-06 09:05:35] [INFO] 商品ID: ''
[2025-08-06 09:05:35] [INFO] 订单ID: ''
[2025-08-06 09:06:25] [INFO] === 新的访问开始 ===
[2025-08-06 09:06:25] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:06:25] [INFO] 请求方法: GET
[2025-08-06 09:06:25] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:06:25] [INFO] POST参数: []
[2025-08-06 09:06:25] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:06:25] [INFO] 客户端IP: ************
[2025-08-06 09:06:25] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:06:25] [INFO] 页面类型: merchant
[2025-08-06 09:06:25] [INFO] 商户ID: '8034567958'
[2025-08-06 09:06:25] [INFO] 商品ID: ''
[2025-08-06 09:06:25] [INFO] 订单ID: ''
[2025-08-06 09:07:28] [INFO] === 新的访问开始 ===
[2025-08-06 09:07:28] [INFO] 请求URI: /shop.php?dd=2025080609044196689
[2025-08-06 09:07:28] [INFO] 请求方法: GET
[2025-08-06 09:07:28] [INFO] GET参数: {"dd":"2025080609044196689"}
[2025-08-06 09:07:28] [INFO] POST参数: []
[2025-08-06 09:07:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:07:28] [INFO] 客户端IP: ************
[2025-08-06 09:07:28] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080609044196689'
[2025-08-06 09:07:28] [INFO] 页面类型: order
[2025-08-06 09:07:28] [INFO] 商户ID: ''
[2025-08-06 09:07:28] [INFO] 商品ID: ''
[2025-08-06 09:07:28] [INFO] 订单ID: '2025080609044196689'
[2025-08-06 09:07:28] [INFO] === 新的访问开始 ===
[2025-08-06 09:07:28] [INFO] 请求URI: /shop.php?dd=2025080609044196689
[2025-08-06 09:07:28] [INFO] 请求方法: GET
[2025-08-06 09:07:28] [INFO] GET参数: {"dd":"2025080609044196689"}
[2025-08-06 09:07:28] [INFO] POST参数: []
[2025-08-06 09:07:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:07:28] [INFO] 客户端IP: ************
[2025-08-06 09:07:28] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080609044196689'
[2025-08-06 09:07:28] [INFO] 页面类型: order
[2025-08-06 09:07:28] [INFO] 商户ID: ''
[2025-08-06 09:07:28] [INFO] 商品ID: ''
[2025-08-06 09:07:28] [INFO] 订单ID: '2025080609044196689'
[2025-08-06 09:07:44] [INFO] === 新的访问开始 ===
[2025-08-06 09:07:44] [INFO] 请求URI: /shop.php?dd=2025080609044196689
[2025-08-06 09:07:44] [INFO] 请求方法: GET
[2025-08-06 09:07:44] [INFO] GET参数: {"dd":"2025080609044196689"}
[2025-08-06 09:07:44] [INFO] POST参数: []
[2025-08-06 09:07:44] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:07:44] [INFO] 客户端IP: ************
[2025-08-06 09:07:44] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080609044196689'
[2025-08-06 09:07:44] [INFO] 页面类型: order
[2025-08-06 09:07:44] [INFO] 商户ID: ''
[2025-08-06 09:07:44] [INFO] 商品ID: ''
[2025-08-06 09:07:44] [INFO] 订单ID: '2025080609044196689'
[2025-08-06 09:08:28] [INFO] === 新的访问开始 ===
[2025-08-06 09:08:28] [INFO] 请求URI: /shop.php?dd=2025080622001476141442892885
[2025-08-06 09:08:28] [INFO] 请求方法: GET
[2025-08-06 09:08:28] [INFO] GET参数: {"dd":"2025080622001476141442892885"}
[2025-08-06 09:08:28] [INFO] POST参数: []
[2025-08-06 09:08:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:08:28] [INFO] 客户端IP: ************
[2025-08-06 09:08:28] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080622001476141442892885'
[2025-08-06 09:08:28] [INFO] 页面类型: order
[2025-08-06 09:08:28] [INFO] 商户ID: ''
[2025-08-06 09:08:28] [INFO] 商品ID: ''
[2025-08-06 09:08:28] [INFO] 订单ID: '2025080622001476141442892885'
[2025-08-06 09:08:35] [INFO] === 新的访问开始 ===
[2025-08-06 09:08:35] [INFO] 请求URI: /shop.php?dd=2025080622001476141442892885
[2025-08-06 09:08:35] [INFO] 请求方法: GET
[2025-08-06 09:08:35] [INFO] GET参数: {"dd":"2025080622001476141442892885"}
[2025-08-06 09:08:35] [INFO] POST参数: []
[2025-08-06 09:08:35] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:08:35] [INFO] 客户端IP: ************
[2025-08-06 09:08:35] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080622001476141442892885'
[2025-08-06 09:08:35] [INFO] 页面类型: order
[2025-08-06 09:08:35] [INFO] 商户ID: ''
[2025-08-06 09:08:35] [INFO] 商品ID: ''
[2025-08-06 09:08:35] [INFO] 订单ID: '2025080622001476141442892885'
[2025-08-06 09:09:13] [INFO] === 新的访问开始 ===
[2025-08-06 09:09:13] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:09:13] [INFO] 请求方法: GET
[2025-08-06 09:09:13] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:09:13] [INFO] POST参数: []
[2025-08-06 09:09:13] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:09:13] [INFO] 客户端IP: ************
[2025-08-06 09:09:13] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:09:13] [INFO] 页面类型: merchant
[2025-08-06 09:09:13] [INFO] 商户ID: '8034567958'
[2025-08-06 09:09:13] [INFO] 商品ID: ''
[2025-08-06 09:09:13] [INFO] 订单ID: ''
[2025-08-06 09:12:09] [INFO] === 新的访问开始 ===
[2025-08-06 09:12:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:12:09] [INFO] 请求方法: GET
[2025-08-06 09:12:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:12:09] [INFO] POST参数: []
[2025-08-06 09:12:09] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:12:09] [INFO] 客户端IP: **************
[2025-08-06 09:12:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:12:09] [INFO] 页面类型: merchant
[2025-08-06 09:12:09] [INFO] 商户ID: '8034567958'
[2025-08-06 09:12:09] [INFO] 商品ID: ''
[2025-08-06 09:12:09] [INFO] 订单ID: ''
[2025-08-06 09:12:17] [INFO] === 新的访问开始 ===
[2025-08-06 09:12:17] [INFO] 请求URI: /shop.php?dd=2025080609044196689
[2025-08-06 09:12:17] [INFO] 请求方法: GET
[2025-08-06 09:12:17] [INFO] GET参数: {"dd":"2025080609044196689"}
[2025-08-06 09:12:17] [INFO] POST参数: []
[2025-08-06 09:12:17] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 09:12:17] [INFO] 客户端IP: **************
[2025-08-06 09:12:17] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080609044196689'
[2025-08-06 09:12:17] [INFO] 页面类型: order
[2025-08-06 09:12:17] [INFO] 商户ID: ''
[2025-08-06 09:12:17] [INFO] 商品ID: ''
[2025-08-06 09:12:17] [INFO] 订单ID: '2025080609044196689'
[2025-08-06 09:37:07] [INFO] === 新的访问开始 ===
[2025-08-06 09:37:07] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:37:07] [INFO] 请求方法: GET
[2025-08-06 09:37:07] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:37:07] [INFO] POST参数: []
[2025-08-06 09:37:07] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 09:37:07] [INFO] 客户端IP: *************
[2025-08-06 09:37:07] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:37:07] [INFO] 页面类型: merchant
[2025-08-06 09:37:07] [INFO] 商户ID: '8034567958'
[2025-08-06 09:37:07] [INFO] 商品ID: ''
[2025-08-06 09:37:07] [INFO] 订单ID: ''
[2025-08-06 09:37:20] [INFO] === 新的访问开始 ===
[2025-08-06 09:37:20] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 09:37:20] [INFO] 请求方法: GET
[2025-08-06 09:37:20] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 09:37:20] [INFO] POST参数: []
[2025-08-06 09:37:20] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 09:37:20] [INFO] 客户端IP: *************
[2025-08-06 09:37:20] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 09:37:20] [INFO] 页面类型: order
[2025-08-06 09:37:20] [INFO] 商户ID: ''
[2025-08-06 09:37:20] [INFO] 商品ID: ''
[2025-08-06 09:37:20] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 09:45:06] [INFO] === 新的访问开始 ===
[2025-08-06 09:45:06] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 09:45:06] [INFO] 请求方法: GET
[2025-08-06 09:45:06] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 09:45:06] [INFO] POST参数: []
[2025-08-06 09:45:06] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 09:45:06] [INFO] 客户端IP: *************
[2025-08-06 09:45:06] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 09:45:06] [INFO] 页面类型: order
[2025-08-06 09:45:06] [INFO] 商户ID: ''
[2025-08-06 09:45:06] [INFO] 商品ID: ''
[2025-08-06 09:45:06] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 09:51:27] [INFO] === 新的访问开始 ===
[2025-08-06 09:51:27] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 09:51:27] [INFO] 请求方法: GET
[2025-08-06 09:51:27] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 09:51:27] [INFO] POST参数: []
[2025-08-06 09:51:27] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-06 09:51:27] [INFO] 客户端IP: **************
[2025-08-06 09:51:27] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 09:51:27] [INFO] 页面类型: order
[2025-08-06 09:51:27] [INFO] 商户ID: ''
[2025-08-06 09:51:27] [INFO] 商品ID: ''
[2025-08-06 09:51:27] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 09:52:57] [INFO] === 新的访问开始 ===
[2025-08-06 09:52:57] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 09:52:57] [INFO] 请求方法: GET
[2025-08-06 09:52:57] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 09:52:57] [INFO] POST参数: []
[2025-08-06 09:52:57] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 09:52:57] [INFO] 客户端IP: **************
[2025-08-06 09:52:57] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 09:52:57] [INFO] 页面类型: order
[2025-08-06 09:52:57] [INFO] 商户ID: ''
[2025-08-06 09:52:57] [INFO] 商品ID: ''
[2025-08-06 09:52:57] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 09:57:31] [INFO] === 新的访问开始 ===
[2025-08-06 09:57:31] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 09:57:31] [INFO] 请求方法: GET
[2025-08-06 09:57:31] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 09:57:31] [INFO] POST参数: []
[2025-08-06 09:57:31] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 09:57:31] [INFO] 客户端IP: **************
[2025-08-06 09:57:31] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 09:57:31] [INFO] 页面类型: merchant
[2025-08-06 09:57:31] [INFO] 商户ID: '8034567958'
[2025-08-06 09:57:31] [INFO] 商品ID: ''
[2025-08-06 09:57:31] [INFO] 订单ID: ''
[2025-08-06 09:57:36] [INFO] === 新的访问开始 ===
[2025-08-06 09:57:36] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 09:57:36] [INFO] 请求方法: GET
[2025-08-06 09:57:36] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 09:57:36] [INFO] POST参数: []
[2025-08-06 09:57:36] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 09:57:36] [INFO] 客户端IP: **************
[2025-08-06 09:57:36] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 09:57:36] [INFO] 页面类型: order
[2025-08-06 09:57:36] [INFO] 商户ID: ''
[2025-08-06 09:57:36] [INFO] 商品ID: ''
[2025-08-06 09:57:36] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 09:59:52] [INFO] === 新的访问开始 ===
[2025-08-06 09:59:52] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 09:59:52] [INFO] 请求方法: GET
[2025-08-06 09:59:52] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 09:59:52] [INFO] POST参数: []
[2025-08-06 09:59:52] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 09:59:52] [INFO] 客户端IP: **************
[2025-08-06 09:59:52] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 09:59:52] [INFO] 页面类型: order
[2025-08-06 09:59:52] [INFO] 商户ID: ''
[2025-08-06 09:59:52] [INFO] 商品ID: ''
[2025-08-06 09:59:52] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 10:00:59] [INFO] === 新的访问开始 ===
[2025-08-06 10:00:59] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 10:00:59] [INFO] 请求方法: GET
[2025-08-06 10:00:59] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 10:00:59] [INFO] POST参数: []
[2025-08-06 10:00:59] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 10:00:59] [INFO] 客户端IP: **************
[2025-08-06 10:00:59] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 10:00:59] [INFO] 页面类型: merchant
[2025-08-06 10:00:59] [INFO] 商户ID: '8034567958'
[2025-08-06 10:00:59] [INFO] 商品ID: ''
[2025-08-06 10:00:59] [INFO] 订单ID: ''
[2025-08-06 10:03:24] [INFO] === 新的访问开始 ===
[2025-08-06 10:03:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 10:03:24] [INFO] 请求方法: GET
[2025-08-06 10:03:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 10:03:24] [INFO] POST参数: []
[2025-08-06 10:03:24] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 10:03:24] [INFO] 客户端IP: ************
[2025-08-06 10:03:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 10:03:24] [INFO] 页面类型: merchant
[2025-08-06 10:03:24] [INFO] 商户ID: '8034567958'
[2025-08-06 10:03:24] [INFO] 商品ID: ''
[2025-08-06 10:03:24] [INFO] 订单ID: ''
[2025-08-06 10:03:39] [INFO] === 新的访问开始 ===
[2025-08-06 10:03:39] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 10:03:39] [INFO] 请求方法: GET
[2025-08-06 10:03:39] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 10:03:39] [INFO] POST参数: []
[2025-08-06 10:03:39] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 10:03:39] [INFO] 客户端IP: ************
[2025-08-06 10:03:39] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 10:03:39] [INFO] 页面类型: order
[2025-08-06 10:03:39] [INFO] 商户ID: ''
[2025-08-06 10:03:39] [INFO] 商品ID: ''
[2025-08-06 10:03:39] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 11:14:02] [INFO] === 新的访问开始 ===
[2025-08-06 11:14:02] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 11:14:02] [INFO] 请求方法: GET
[2025-08-06 11:14:02] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 11:14:02] [INFO] POST参数: []
[2025-08-06 11:14:02] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-06 11:14:02] [INFO] 客户端IP: **************
[2025-08-06 11:14:02] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 11:14:02] [INFO] 页面类型: merchant
[2025-08-06 11:14:02] [INFO] 商户ID: '8034567958'
[2025-08-06 11:14:02] [INFO] 商品ID: ''
[2025-08-06 11:14:02] [INFO] 订单ID: ''
[2025-08-06 11:14:45] [INFO] === 新的访问开始 ===
[2025-08-06 11:14:45] [INFO] 请求URI: /shop.php?sp=23
[2025-08-06 11:14:45] [INFO] 请求方法: GET
[2025-08-06 11:14:45] [INFO] GET参数: {"sp":"23"}
[2025-08-06 11:14:45] [INFO] POST参数: []
[2025-08-06 11:14:45] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.157 Mobile Safari/537.36
[2025-08-06 11:14:45] [INFO] 客户端IP: **************
[2025-08-06 11:14:45] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-06 11:14:45] [INFO] 页面类型: product
[2025-08-06 11:14:45] [INFO] 商户ID: ''
[2025-08-06 11:14:45] [INFO] 商品ID: '23'
[2025-08-06 11:14:45] [INFO] 订单ID: ''
[2025-08-06 11:17:43] [INFO] === 新的访问开始 ===
[2025-08-06 11:17:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 11:17:43] [INFO] 请求方法: GET
[2025-08-06 11:17:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 11:17:43] [INFO] POST参数: []
[2025-08-06 11:17:43] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.157 Mobile Safari/537.36
[2025-08-06 11:17:43] [INFO] 客户端IP: **************
[2025-08-06 11:17:43] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 11:17:43] [INFO] 页面类型: merchant
[2025-08-06 11:17:43] [INFO] 商户ID: '8034567958'
[2025-08-06 11:17:43] [INFO] 商品ID: ''
[2025-08-06 11:17:43] [INFO] 订单ID: ''
[2025-08-06 11:18:21] [INFO] === 新的访问开始 ===
[2025-08-06 11:18:21] [INFO] 请求URI: /shop.php?sp=23
[2025-08-06 11:18:21] [INFO] 请求方法: GET
[2025-08-06 11:18:21] [INFO] GET参数: {"sp":"23"}
[2025-08-06 11:18:21] [INFO] POST参数: []
[2025-08-06 11:18:21] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.157 Mobile Safari/537.36
[2025-08-06 11:18:21] [INFO] 客户端IP: **************
[2025-08-06 11:18:21] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-06 11:18:21] [INFO] 页面类型: product
[2025-08-06 11:18:21] [INFO] 商户ID: ''
[2025-08-06 11:18:21] [INFO] 商品ID: '23'
[2025-08-06 11:18:21] [INFO] 订单ID: ''
[2025-08-06 11:28:02] [INFO] === 新的访问开始 ===
[2025-08-06 11:28:02] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 11:28:02] [INFO] 请求方法: GET
[2025-08-06 11:28:02] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 11:28:02] [INFO] POST参数: []
[2025-08-06 11:28:02] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.157 Mobile Safari/537.36
[2025-08-06 11:28:02] [INFO] 客户端IP: **************
[2025-08-06 11:28:02] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 11:28:02] [INFO] 页面类型: merchant
[2025-08-06 11:28:02] [INFO] 商户ID: '8034567958'
[2025-08-06 11:28:02] [INFO] 商品ID: ''
[2025-08-06 11:28:02] [INFO] 订单ID: ''
[2025-08-06 11:28:50] [INFO] === 新的访问开始 ===
[2025-08-06 11:28:50] [INFO] 请求URI: /shop.php?dd=ORDER17544231544073
[2025-08-06 11:28:50] [INFO] 请求方法: GET
[2025-08-06 11:28:50] [INFO] GET参数: {"dd":"ORDER17544231544073"}
[2025-08-06 11:28:50] [INFO] POST参数: []
[2025-08-06 11:28:50] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 23076RA4BC Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/19.9.60728
[2025-08-06 11:28:50] [INFO] 客户端IP: **************
[2025-08-06 11:28:50] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17544231544073'
[2025-08-06 11:28:50] [INFO] 页面类型: order
[2025-08-06 11:28:50] [INFO] 商户ID: ''
[2025-08-06 11:28:50] [INFO] 商品ID: ''
[2025-08-06 11:28:50] [INFO] 订单ID: 'ORDER17544231544073'
[2025-08-06 11:45:32] [INFO] === 新的访问开始 ===
[2025-08-06 11:45:32] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 11:45:32] [INFO] 请求方法: GET
[2025-08-06 11:45:32] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 11:45:32] [INFO] POST参数: []
[2025-08-06 11:45:32] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1
[2025-08-06 11:45:32] [INFO] 客户端IP: *************
[2025-08-06 11:45:32] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 11:45:32] [INFO] 页面类型: merchant
[2025-08-06 11:45:32] [INFO] 商户ID: '8034567958'
[2025-08-06 11:45:32] [INFO] 商品ID: ''
[2025-08-06 11:45:32] [INFO] 订单ID: ''
[2025-08-06 11:55:17] [INFO] === 新的访问开始 ===
[2025-08-06 11:55:17] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 11:55:17] [INFO] 请求方法: GET
[2025-08-06 11:55:17] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 11:55:17] [INFO] POST参数: []
[2025-08-06 11:55:17] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1
[2025-08-06 11:55:17] [INFO] 客户端IP: *************
[2025-08-06 11:55:17] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 11:55:17] [INFO] 页面类型: merchant
[2025-08-06 11:55:17] [INFO] 商户ID: '8034567958'
[2025-08-06 11:55:17] [INFO] 商品ID: ''
[2025-08-06 11:55:17] [INFO] 订单ID: ''
[2025-08-06 11:57:03] [INFO] === 新的访问开始 ===
[2025-08-06 11:57:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 11:57:03] [INFO] 请求方法: GET
[2025-08-06 11:57:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 11:57:03] [INFO] POST参数: []
[2025-08-06 11:57:03] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Mobile/15E148 Safari/604.1
[2025-08-06 11:57:03] [INFO] 客户端IP: *************
[2025-08-06 11:57:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 11:57:03] [INFO] 页面类型: merchant
[2025-08-06 11:57:03] [INFO] 商户ID: '8034567958'
[2025-08-06 11:57:03] [INFO] 商品ID: ''
[2025-08-06 11:57:03] [INFO] 订单ID: ''
[2025-08-06 11:59:01] [INFO] === 新的访问开始 ===
[2025-08-06 11:59:01] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 11:59:01] [INFO] 请求方法: GET
[2025-08-06 11:59:01] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 11:59:01] [INFO] POST参数: []
[2025-08-06 11:59:01] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Mobile/15E148 Safari/604.1
[2025-08-06 11:59:01] [INFO] 客户端IP: **************
[2025-08-06 11:59:01] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 11:59:01] [INFO] 页面类型: merchant
[2025-08-06 11:59:01] [INFO] 商户ID: '8034567958'
[2025-08-06 11:59:01] [INFO] 商品ID: ''
[2025-08-06 11:59:01] [INFO] 订单ID: ''
[2025-08-06 12:35:56] [INFO] === 新的访问开始 ===
[2025-08-06 12:35:56] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 12:35:56] [INFO] 请求方法: GET
[2025-08-06 12:35:56] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 12:35:56] [INFO] POST参数: []
[2025-08-06 12:35:56] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2425A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 12:35:56] [INFO] 客户端IP: ************
[2025-08-06 12:35:56] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 12:35:56] [INFO] 页面类型: merchant
[2025-08-06 12:35:56] [INFO] 商户ID: '8034567958'
[2025-08-06 12:35:56] [INFO] 商品ID: ''
[2025-08-06 12:35:56] [INFO] 订单ID: ''
[2025-08-06 12:36:45] [INFO] === 新的访问开始 ===
[2025-08-06 12:36:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 12:36:45] [INFO] 请求方法: GET
[2025-08-06 12:36:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 12:36:45] [INFO] POST参数: []
[2025-08-06 12:36:45] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2425A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 12:36:45] [INFO] 客户端IP: **************
[2025-08-06 12:36:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 12:36:45] [INFO] 页面类型: merchant
[2025-08-06 12:36:45] [INFO] 商户ID: '8034567958'
[2025-08-06 12:36:45] [INFO] 商品ID: ''
[2025-08-06 12:36:45] [INFO] 订单ID: ''
[2025-08-06 12:38:50] [INFO] === 新的访问开始 ===
[2025-08-06 12:38:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 12:38:50] [INFO] 请求方法: GET
[2025-08-06 12:38:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 12:38:50] [INFO] POST参数: []
[2025-08-06 12:38:50] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2425A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 12:38:50] [INFO] 客户端IP: **************
[2025-08-06 12:38:50] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 12:38:50] [INFO] 页面类型: merchant
[2025-08-06 12:38:50] [INFO] 商户ID: '8034567958'
[2025-08-06 12:38:50] [INFO] 商品ID: ''
[2025-08-06 12:38:50] [INFO] 订单ID: ''
[2025-08-06 12:43:06] [INFO] === 新的访问开始 ===
[2025-08-06 12:43:06] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 12:43:06] [INFO] 请求方法: GET
[2025-08-06 12:43:06] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 12:43:06] [INFO] POST参数: []
[2025-08-06 12:43:06] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2425A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 12:43:06] [INFO] 客户端IP: **************
[2025-08-06 12:43:06] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 12:43:06] [INFO] 页面类型: merchant
[2025-08-06 12:43:06] [INFO] 商户ID: '8034567958'
[2025-08-06 12:43:06] [INFO] 商品ID: ''
[2025-08-06 12:43:06] [INFO] 订单ID: ''
[2025-08-06 13:04:20] [INFO] === 新的访问开始 ===
[2025-08-06 13:04:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 13:04:20] [INFO] 请求方法: GET
[2025-08-06 13:04:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 13:04:20] [INFO] POST参数: []
[2025-08-06 13:04:20] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-06 13:04:20] [INFO] 客户端IP: **************
[2025-08-06 13:04:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 13:04:20] [INFO] 页面类型: merchant
[2025-08-06 13:04:20] [INFO] 商户ID: '8034567958'
[2025-08-06 13:04:20] [INFO] 商品ID: ''
[2025-08-06 13:04:20] [INFO] 订单ID: ''
[2025-08-06 13:04:37] [INFO] === 新的访问开始 ===
[2025-08-06 13:04:37] [INFO] 请求URI: /shop.php?sp=23
[2025-08-06 13:04:37] [INFO] 请求方法: GET
[2025-08-06 13:04:37] [INFO] GET参数: {"sp":"23"}
[2025-08-06 13:04:37] [INFO] POST参数: []
[2025-08-06 13:04:37] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.58 Mobile Safari/537.36
[2025-08-06 13:04:37] [INFO] 客户端IP: **************
[2025-08-06 13:04:37] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-06 13:04:37] [INFO] 页面类型: product
[2025-08-06 13:04:37] [INFO] 商户ID: ''
[2025-08-06 13:04:37] [INFO] 商品ID: '23'
[2025-08-06 13:04:37] [INFO] 订单ID: ''
[2025-08-06 14:08:07] [INFO] === 新的访问开始 ===
[2025-08-06 14:08:07] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:08:07] [INFO] 请求方法: GET
[2025-08-06 14:08:07] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:08:07] [INFO] POST参数: []
[2025-08-06 14:08:07] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.71 Mobile Safari/537.36
[2025-08-06 14:08:07] [INFO] 客户端IP: **************
[2025-08-06 14:08:07] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:08:07] [INFO] 页面类型: merchant
[2025-08-06 14:08:07] [INFO] 商户ID: '8034567958'
[2025-08-06 14:08:07] [INFO] 商品ID: ''
[2025-08-06 14:08:07] [INFO] 订单ID: ''
[2025-08-06 14:08:16] [INFO] === 新的访问开始 ===
[2025-08-06 14:08:16] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:08:16] [INFO] 请求方法: GET
[2025-08-06 14:08:16] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:08:16] [INFO] POST参数: []
[2025-08-06 14:08:16] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 14:08:16] [INFO] 客户端IP: **************
[2025-08-06 14:08:16] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:08:16] [INFO] 页面类型: merchant
[2025-08-06 14:08:16] [INFO] 商户ID: '8034567958'
[2025-08-06 14:08:16] [INFO] 商品ID: ''
[2025-08-06 14:08:16] [INFO] 订单ID: ''
[2025-08-06 14:08:59] [INFO] === 新的访问开始 ===
[2025-08-06 14:08:59] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:08:59] [INFO] 请求方法: GET
[2025-08-06 14:08:59] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:08:59] [INFO] POST参数: []
[2025-08-06 14:08:59] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 14:08:59] [INFO] 客户端IP: ************
[2025-08-06 14:08:59] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:08:59] [INFO] 页面类型: merchant
[2025-08-06 14:08:59] [INFO] 商户ID: '8034567958'
[2025-08-06 14:08:59] [INFO] 商品ID: ''
[2025-08-06 14:08:59] [INFO] 订单ID: ''
[2025-08-06 14:09:31] [INFO] === 新的访问开始 ===
[2025-08-06 14:09:31] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:09:31] [INFO] 请求方法: GET
[2025-08-06 14:09:31] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:09:31] [INFO] POST参数: []
[2025-08-06 14:09:31] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 14:09:31] [INFO] 客户端IP: ************
[2025-08-06 14:09:31] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:09:31] [INFO] 页面类型: merchant
[2025-08-06 14:09:31] [INFO] 商户ID: '8034567958'
[2025-08-06 14:09:31] [INFO] 商品ID: ''
[2025-08-06 14:09:31] [INFO] 订单ID: ''
[2025-08-06 14:09:40] [INFO] === 新的访问开始 ===
[2025-08-06 14:09:40] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:09:40] [INFO] 请求方法: GET
[2025-08-06 14:09:40] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:09:40] [INFO] POST参数: []
[2025-08-06 14:09:40] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 14:09:40] [INFO] 客户端IP: ************
[2025-08-06 14:09:40] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:09:40] [INFO] 页面类型: merchant
[2025-08-06 14:09:40] [INFO] 商户ID: '8034567958'
[2025-08-06 14:09:40] [INFO] 商品ID: ''
[2025-08-06 14:09:40] [INFO] 订单ID: ''
[2025-08-06 14:09:54] [INFO] === 新的访问开始 ===
[2025-08-06 14:09:54] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:09:54] [INFO] 请求方法: GET
[2025-08-06 14:09:54] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:09:54] [INFO] POST参数: []
[2025-08-06 14:09:54] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 14:09:54] [INFO] 客户端IP: ************
[2025-08-06 14:09:54] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:09:54] [INFO] 页面类型: merchant
[2025-08-06 14:09:54] [INFO] 商户ID: '8034567958'
[2025-08-06 14:09:54] [INFO] 商品ID: ''
[2025-08-06 14:09:54] [INFO] 订单ID: ''
[2025-08-06 14:10:56] [INFO] === 新的访问开始 ===
[2025-08-06 14:10:56] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:10:56] [INFO] 请求方法: GET
[2025-08-06 14:10:56] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:10:56] [INFO] POST参数: []
[2025-08-06 14:10:56] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 14:10:56] [INFO] 客户端IP: ************
[2025-08-06 14:10:56] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:10:56] [INFO] 页面类型: merchant
[2025-08-06 14:10:56] [INFO] 商户ID: '8034567958'
[2025-08-06 14:10:56] [INFO] 商品ID: ''
[2025-08-06 14:10:56] [INFO] 订单ID: ''
[2025-08-06 14:13:48] [INFO] === 新的访问开始 ===
[2025-08-06 14:13:48] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:13:48] [INFO] 请求方法: GET
[2025-08-06 14:13:48] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:13:48] [INFO] POST参数: []
[2025-08-06 14:13:48] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.71 Mobile Safari/537.36
[2025-08-06 14:13:48] [INFO] 客户端IP: *************
[2025-08-06 14:13:48] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:13:48] [INFO] 页面类型: merchant
[2025-08-06 14:13:48] [INFO] 商户ID: '8034567958'
[2025-08-06 14:13:48] [INFO] 商品ID: ''
[2025-08-06 14:13:48] [INFO] 订单ID: ''
[2025-08-06 14:15:53] [INFO] === 新的访问开始 ===
[2025-08-06 14:15:53] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:15:53] [INFO] 请求方法: GET
[2025-08-06 14:15:53] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:15:53] [INFO] POST参数: []
[2025-08-06 14:15:53] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 11; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36
[2025-08-06 14:15:53] [INFO] 客户端IP: **************
[2025-08-06 14:15:53] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:15:53] [INFO] 页面类型: merchant
[2025-08-06 14:15:53] [INFO] 商户ID: '8034567958'
[2025-08-06 14:15:53] [INFO] 商品ID: ''
[2025-08-06 14:15:53] [INFO] 订单ID: ''
[2025-08-06 14:18:22] [INFO] === 新的访问开始 ===
[2025-08-06 14:18:22] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:18:22] [INFO] 请求方法: GET
[2025-08-06 14:18:22] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:18:22] [INFO] POST参数: []
[2025-08-06 14:18:22] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.71 Mobile Safari/537.36
[2025-08-06 14:18:22] [INFO] 客户端IP: **************
[2025-08-06 14:18:22] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:18:22] [INFO] 页面类型: merchant
[2025-08-06 14:18:22] [INFO] 商户ID: '8034567958'
[2025-08-06 14:18:22] [INFO] 商品ID: ''
[2025-08-06 14:18:22] [INFO] 订单ID: ''
[2025-08-06 14:18:28] [INFO] === 新的访问开始 ===
[2025-08-06 14:18:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:18:28] [INFO] 请求方法: GET
[2025-08-06 14:18:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:18:28] [INFO] POST参数: []
[2025-08-06 14:18:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 14:18:28] [INFO] 客户端IP: **************
[2025-08-06 14:18:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:18:28] [INFO] 页面类型: merchant
[2025-08-06 14:18:28] [INFO] 商户ID: '8034567958'
[2025-08-06 14:18:28] [INFO] 商品ID: ''
[2025-08-06 14:18:28] [INFO] 订单ID: ''
[2025-08-06 14:40:33] [INFO] === 新的访问开始 ===
[2025-08-06 14:40:33] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:40:33] [INFO] 请求方法: GET
[2025-08-06 14:40:33] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:40:33] [INFO] POST参数: []
[2025-08-06 14:40:33] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.71 Mobile Safari/537.36
[2025-08-06 14:40:33] [INFO] 客户端IP: ************
[2025-08-06 14:40:33] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:40:33] [INFO] 页面类型: merchant
[2025-08-06 14:40:33] [INFO] 商户ID: '8034567958'
[2025-08-06 14:40:33] [INFO] 商品ID: ''
[2025-08-06 14:40:33] [INFO] 订单ID: ''
[2025-08-06 14:41:05] [INFO] === 新的访问开始 ===
[2025-08-06 14:41:05] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 14:41:05] [INFO] 请求方法: GET
[2025-08-06 14:41:05] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 14:41:05] [INFO] POST参数: []
[2025-08-06 14:41:05] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.71 Mobile Safari/537.36
[2025-08-06 14:41:05] [INFO] 客户端IP: ************
[2025-08-06 14:41:05] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 14:41:05] [INFO] 页面类型: merchant
[2025-08-06 14:41:05] [INFO] 商户ID: '8034567958'
[2025-08-06 14:41:05] [INFO] 商品ID: ''
[2025-08-06 14:41:05] [INFO] 订单ID: ''
[2025-08-06 15:28:30] [INFO] === 新的访问开始 ===
[2025-08-06 15:28:30] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 15:28:30] [INFO] 请求方法: GET
[2025-08-06 15:28:30] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 15:28:30] [INFO] POST参数: []
[2025-08-06 15:28:30] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 15:28:30] [INFO] 客户端IP: **************
[2025-08-06 15:28:30] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 15:28:30] [INFO] 页面类型: merchant
[2025-08-06 15:28:30] [INFO] 商户ID: '8034567958'
[2025-08-06 15:28:30] [INFO] 商品ID: ''
[2025-08-06 15:28:30] [INFO] 订单ID: ''
[2025-08-06 15:48:50] [INFO] === 新的访问开始 ===
[2025-08-06 15:48:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 15:48:50] [INFO] 请求方法: GET
[2025-08-06 15:48:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 15:48:50] [INFO] POST参数: []
[2025-08-06 15:48:50] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.67 Mobile Safari/537.36
[2025-08-06 15:48:50] [INFO] 客户端IP: ************
[2025-08-06 15:48:50] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 15:48:50] [INFO] 页面类型: merchant
[2025-08-06 15:48:50] [INFO] 商户ID: '8034567958'
[2025-08-06 15:48:50] [INFO] 商品ID: ''
[2025-08-06 15:48:50] [INFO] 订单ID: ''
[2025-08-06 17:05:18] [INFO] === 新的访问开始 ===
[2025-08-06 17:05:18] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 17:05:18] [INFO] 请求方法: GET
[2025-08-06 17:05:18] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 17:05:18] [INFO] POST参数: []
[2025-08-06 17:05:18] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 17:05:18] [INFO] 客户端IP: ************
[2025-08-06 17:05:18] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 17:05:18] [INFO] 页面类型: merchant
[2025-08-06 17:05:18] [INFO] 商户ID: '8034567958'
[2025-08-06 17:05:18] [INFO] 商品ID: ''
[2025-08-06 17:05:18] [INFO] 订单ID: ''
[2025-08-06 17:09:49] [INFO] === 新的访问开始 ===
[2025-08-06 17:09:49] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 17:09:49] [INFO] 请求方法: GET
[2025-08-06 17:09:49] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 17:09:49] [INFO] POST参数: []
[2025-08-06 17:09:49] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-06 17:09:49] [INFO] 客户端IP: *************
[2025-08-06 17:09:49] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 17:09:49] [INFO] 页面类型: merchant
[2025-08-06 17:09:49] [INFO] 商户ID: '8034567958'
[2025-08-06 17:09:49] [INFO] 商品ID: ''
[2025-08-06 17:09:49] [INFO] 订单ID: ''
[2025-08-06 17:22:34] [INFO] === 新的访问开始 ===
[2025-08-06 17:22:34] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 17:22:34] [INFO] 请求方法: GET
[2025-08-06 17:22:34] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 17:22:34] [INFO] POST参数: []
[2025-08-06 17:22:34] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36 QuarkPC/4.3.0.469
[2025-08-06 17:22:34] [INFO] 客户端IP: **************
[2025-08-06 17:22:34] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 17:22:34] [INFO] 页面类型: merchant
[2025-08-06 17:22:34] [INFO] 商户ID: '8034567958'
[2025-08-06 17:22:34] [INFO] 商品ID: ''
[2025-08-06 17:22:34] [INFO] 订单ID: ''
[2025-08-06 18:31:12] [INFO] === 新的访问开始 ===
[2025-08-06 18:31:12] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 18:31:12] [INFO] 请求方法: GET
[2025-08-06 18:31:12] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 18:31:12] [INFO] POST参数: []
[2025-08-06 18:31:12] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; V2353A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-06 18:31:12] [INFO] 客户端IP: *************
[2025-08-06 18:31:12] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 18:31:12] [INFO] 页面类型: merchant
[2025-08-06 18:31:12] [INFO] 商户ID: '8034567958'
[2025-08-06 18:31:12] [INFO] 商品ID: ''
[2025-08-06 18:31:12] [INFO] 订单ID: ''
[2025-08-06 18:32:32] [INFO] === 新的访问开始 ===
[2025-08-06 18:32:32] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 18:32:32] [INFO] 请求方法: GET
[2025-08-06 18:32:32] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 18:32:32] [INFO] POST参数: []
[2025-08-06 18:32:32] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 18:32:32] [INFO] 客户端IP: **************
[2025-08-06 18:32:32] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 18:32:32] [INFO] 页面类型: merchant
[2025-08-06 18:32:32] [INFO] 商户ID: '8034567958'
[2025-08-06 18:32:32] [INFO] 商品ID: ''
[2025-08-06 18:32:32] [INFO] 订单ID: ''
[2025-08-06 18:34:17] [INFO] === 新的访问开始 ===
[2025-08-06 18:34:17] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 18:34:17] [INFO] 请求方法: GET
[2025-08-06 18:34:17] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 18:34:17] [INFO] POST参数: []
[2025-08-06 18:34:17] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-06 18:34:17] [INFO] 客户端IP: **************
[2025-08-06 18:34:17] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 18:34:17] [INFO] 页面类型: merchant
[2025-08-06 18:34:17] [INFO] 商户ID: '8034567958'
[2025-08-06 18:34:17] [INFO] 商品ID: ''
[2025-08-06 18:34:17] [INFO] 订单ID: ''
[2025-08-06 18:37:44] [INFO] === 新的访问开始 ===
[2025-08-06 18:37:44] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 18:37:44] [INFO] 请求方法: GET
[2025-08-06 18:37:44] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 18:37:44] [INFO] POST参数: []
[2025-08-06 18:37:44] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.86 Mobile Safari/537.36
[2025-08-06 18:37:44] [INFO] 客户端IP: **************
[2025-08-06 18:37:44] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 18:37:44] [INFO] 页面类型: merchant
[2025-08-06 18:37:44] [INFO] 商户ID: '8034567958'
[2025-08-06 18:37:44] [INFO] 商品ID: ''
[2025-08-06 18:37:44] [INFO] 订单ID: ''
[2025-08-06 18:43:45] [INFO] === 新的访问开始 ===
[2025-08-06 18:43:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 18:43:45] [INFO] 请求方法: GET
[2025-08-06 18:43:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 18:43:45] [INFO] POST参数: []
[2025-08-06 18:43:45] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Mobile/15E148 Safari/604.1
[2025-08-06 18:43:45] [INFO] 客户端IP: ************
[2025-08-06 18:43:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 18:43:45] [INFO] 页面类型: merchant
[2025-08-06 18:43:45] [INFO] 商户ID: '8034567958'
[2025-08-06 18:43:45] [INFO] 商品ID: ''
[2025-08-06 18:43:45] [INFO] 订单ID: ''
[2025-08-06 21:13:11] [INFO] === 新的访问开始 ===
[2025-08-06 21:13:11] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 21:13:11] [INFO] 请求方法: GET
[2025-08-06 21:13:11] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 21:13:11] [INFO] POST参数: []
[2025-08-06 21:13:11] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Mobile/15E148 Safari/604.1
[2025-08-06 21:13:11] [INFO] 客户端IP: **************
[2025-08-06 21:13:11] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 21:13:11] [INFO] 页面类型: merchant
[2025-08-06 21:13:11] [INFO] 商户ID: '8034567958'
[2025-08-06 21:13:11] [INFO] 商品ID: ''
[2025-08-06 21:13:11] [INFO] 订单ID: ''
[2025-08-06 21:13:19] [INFO] === 新的访问开始 ===
[2025-08-06 21:13:19] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-06 21:13:19] [INFO] 请求方法: GET
[2025-08-06 21:13:19] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-06 21:13:19] [INFO] POST参数: []
[2025-08-06 21:13:19] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1.1 Mobile/15E148 Safari/604.1
[2025-08-06 21:13:19] [INFO] 客户端IP: **************
[2025-08-06 21:13:19] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-06 21:13:19] [INFO] 页面类型: merchant
[2025-08-06 21:13:19] [INFO] 商户ID: '8034567958'
[2025-08-06 21:13:19] [INFO] 商品ID: ''
[2025-08-06 21:13:19] [INFO] 订单ID: ''
[2025-08-07 00:13:10] [INFO] === 新的访问开始 ===
[2025-08-07 00:13:10] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 00:13:10] [INFO] 请求方法: GET
[2025-08-07 00:13:10] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 00:13:10] [INFO] POST参数: []
[2025-08-07 00:13:10] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-07 00:13:10] [INFO] 客户端IP: **************
[2025-08-07 00:13:10] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 00:13:10] [INFO] 页面类型: merchant
[2025-08-07 00:13:10] [INFO] 商户ID: '8034567958'
[2025-08-07 00:13:10] [INFO] 商品ID: ''
[2025-08-07 00:13:10] [INFO] 订单ID: ''
[2025-08-07 00:13:37] [INFO] === 新的访问开始 ===
[2025-08-07 00:13:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 00:13:37] [INFO] 请求方法: GET
[2025-08-07 00:13:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 00:13:37] [INFO] POST参数: []
[2025-08-07 00:13:37] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-07 00:13:37] [INFO] 客户端IP: **************
[2025-08-07 00:13:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 00:13:37] [INFO] 页面类型: merchant
[2025-08-07 00:13:37] [INFO] 商户ID: '8034567958'
[2025-08-07 00:13:37] [INFO] 商品ID: ''
[2025-08-07 00:13:37] [INFO] 订单ID: ''
[2025-08-07 01:29:21] [INFO] === 新的访问开始 ===
[2025-08-07 01:29:21] [INFO] 请求URI: /shop.php?sp=23
[2025-08-07 01:29:21] [INFO] 请求方法: GET
[2025-08-07 01:29:21] [INFO] GET参数: {"sp":"23"}
[2025-08-07 01:29:21] [INFO] POST参数: []
[2025-08-07 01:29:21] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/22G86 Safari/604.1
[2025-08-07 01:29:21] [INFO] 客户端IP: *************
[2025-08-07 01:29:21] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-07 01:29:21] [INFO] 页面类型: product
[2025-08-07 01:29:21] [INFO] 商户ID: ''
[2025-08-07 01:29:21] [INFO] 商品ID: '23'
[2025-08-07 01:29:21] [INFO] 订单ID: ''
[2025-08-07 01:31:31] [INFO] === 新的访问开始 ===
[2025-08-07 01:31:31] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 01:31:31] [INFO] 请求方法: GET
[2025-08-07 01:31:31] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 01:31:31] [INFO] POST参数: []
[2025-08-07 01:31:31] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/22G86 Safari/604.1
[2025-08-07 01:31:31] [INFO] 客户端IP: ************
[2025-08-07 01:31:31] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 01:31:31] [INFO] 页面类型: merchant
[2025-08-07 01:31:31] [INFO] 商户ID: '8034567958'
[2025-08-07 01:31:31] [INFO] 商品ID: ''
[2025-08-07 01:31:31] [INFO] 订单ID: ''
[2025-08-07 01:41:04] [INFO] === 新的访问开始 ===
[2025-08-07 01:41:04] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 01:41:04] [INFO] 请求方法: GET
[2025-08-07 01:41:04] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 01:41:04] [INFO] POST参数: []
[2025-08-07 01:41:04] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/22G86 Safari/604.1
[2025-08-07 01:41:04] [INFO] 客户端IP: ************
[2025-08-07 01:41:04] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 01:41:04] [INFO] 页面类型: merchant
[2025-08-07 01:41:04] [INFO] 商户ID: '8034567958'
[2025-08-07 01:41:04] [INFO] 商品ID: ''
[2025-08-07 01:41:04] [INFO] 订单ID: ''
[2025-08-07 01:47:03] [INFO] === 新的访问开始 ===
[2025-08-07 01:47:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 01:47:03] [INFO] 请求方法: GET
[2025-08-07 01:47:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 01:47:03] [INFO] POST参数: []
[2025-08-07 01:47:03] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1
[2025-08-07 01:47:03] [INFO] 客户端IP: *************
[2025-08-07 01:47:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 01:47:03] [INFO] 页面类型: merchant
[2025-08-07 01:47:03] [INFO] 商户ID: '8034567958'
[2025-08-07 01:47:03] [INFO] 商品ID: ''
[2025-08-07 01:47:03] [INFO] 订单ID: ''
[2025-08-07 01:53:32] [INFO] === 新的访问开始 ===
[2025-08-07 01:53:32] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 01:53:32] [INFO] 请求方法: GET
[2025-08-07 01:53:32] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 01:53:32] [INFO] POST参数: []
[2025-08-07 01:53:32] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1
[2025-08-07 01:53:32] [INFO] 客户端IP: ************
[2025-08-07 01:53:32] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 01:53:32] [INFO] 页面类型: merchant
[2025-08-07 01:53:32] [INFO] 商户ID: '8034567958'
[2025-08-07 01:53:32] [INFO] 商品ID: ''
[2025-08-07 01:53:32] [INFO] 订单ID: ''
[2025-08-07 01:56:57] [INFO] === 新的访问开始 ===
[2025-08-07 01:56:57] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 01:56:57] [INFO] 请求方法: GET
[2025-08-07 01:56:57] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 01:56:57] [INFO] POST参数: []
[2025-08-07 01:56:57] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.6 Mobile/15E148 Safari/604.1
[2025-08-07 01:56:57] [INFO] 客户端IP: ************
[2025-08-07 01:56:57] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 01:56:57] [INFO] 页面类型: merchant
[2025-08-07 01:56:57] [INFO] 商户ID: '8034567958'
[2025-08-07 01:56:57] [INFO] 商品ID: ''
[2025-08-07 01:56:57] [INFO] 订单ID: ''
[2025-08-07 03:16:51] [INFO] === 新的访问开始 ===
[2025-08-07 03:16:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 03:16:51] [INFO] 请求方法: GET
[2025-08-07 03:16:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 03:16:51] [INFO] POST参数: []
[2025-08-07 03:16:51] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 8.1.0; 16th Plus Build/OPM1.171019.026; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/65.0.3325.110 Mobile Safari/537.36
[2025-08-07 03:16:51] [INFO] 客户端IP: ************
[2025-08-07 03:16:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 03:16:51] [INFO] 页面类型: merchant
[2025-08-07 03:16:51] [INFO] 商户ID: '8034567958'
[2025-08-07 03:16:51] [INFO] 商品ID: ''
[2025-08-07 03:16:51] [INFO] 订单ID: ''
[2025-08-07 03:41:26] [INFO] === 新的访问开始 ===
[2025-08-07 03:41:26] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 03:41:26] [INFO] 请求方法: GET
[2025-08-07 03:41:26] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 03:41:26] [INFO] POST参数: []
[2025-08-07 03:41:26] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 03:41:26] [INFO] 客户端IP: *************
[2025-08-07 03:41:26] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 03:41:26] [INFO] 页面类型: merchant
[2025-08-07 03:41:26] [INFO] 商户ID: '8034567958'
[2025-08-07 03:41:26] [INFO] 商品ID: ''
[2025-08-07 03:41:26] [INFO] 订单ID: ''
[2025-08-07 03:42:47] [INFO] === 新的访问开始 ===
[2025-08-07 03:42:47] [INFO] 请求URI: /shop.php?sp=23
[2025-08-07 03:42:47] [INFO] 请求方法: GET
[2025-08-07 03:42:47] [INFO] GET参数: {"sp":"23"}
[2025-08-07 03:42:47] [INFO] POST参数: []
[2025-08-07 03:42:47] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 03:42:47] [INFO] 客户端IP: *************
[2025-08-07 03:42:47] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-07 03:42:47] [INFO] 页面类型: product
[2025-08-07 03:42:47] [INFO] 商户ID: ''
[2025-08-07 03:42:47] [INFO] 商品ID: '23'
[2025-08-07 03:42:47] [INFO] 订单ID: ''
[2025-08-07 03:44:07] [INFO] === 新的访问开始 ===
[2025-08-07 03:44:07] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 03:44:07] [INFO] 请求方法: GET
[2025-08-07 03:44:07] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 03:44:07] [INFO] POST参数: []
[2025-08-07 03:44:07] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 03:44:07] [INFO] 客户端IP: **************
[2025-08-07 03:44:07] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 03:44:07] [INFO] 页面类型: merchant
[2025-08-07 03:44:07] [INFO] 商户ID: '8034567958'
[2025-08-07 03:44:07] [INFO] 商品ID: ''
[2025-08-07 03:44:07] [INFO] 订单ID: ''
[2025-08-07 07:12:33] [INFO] === 新的访问开始 ===
[2025-08-07 07:12:33] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 07:12:33] [INFO] 请求方法: GET
[2025-08-07 07:12:33] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 07:12:33] [INFO] POST参数: []
[2025-08-07 07:12:33] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2361A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-07 07:12:33] [INFO] 客户端IP: **************
[2025-08-07 07:12:33] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 07:12:33] [INFO] 页面类型: merchant
[2025-08-07 07:12:33] [INFO] 商户ID: '8034567958'
[2025-08-07 07:12:33] [INFO] 商品ID: ''
[2025-08-07 07:12:33] [INFO] 订单ID: ''
[2025-08-07 08:18:37] [INFO] === 新的访问开始 ===
[2025-08-07 08:18:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 08:18:37] [INFO] 请求方法: GET
[2025-08-07 08:18:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 08:18:37] [INFO] POST参数: []
[2025-08-07 08:18:37] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36
[2025-08-07 08:18:37] [INFO] 客户端IP: *************
[2025-08-07 08:18:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 08:18:37] [INFO] 页面类型: merchant
[2025-08-07 08:18:37] [INFO] 商户ID: '8034567958'
[2025-08-07 08:18:37] [INFO] 商品ID: ''
[2025-08-07 08:18:37] [INFO] 订单ID: ''
[2025-08-07 10:37:42] [INFO] === 新的访问开始 ===
[2025-08-07 10:37:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 10:37:42] [INFO] 请求方法: GET
[2025-08-07 10:37:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 10:37:42] [INFO] POST参数: []
[2025-08-07 10:37:42] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 10:37:42] [INFO] 客户端IP: **************
[2025-08-07 10:37:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 10:37:42] [INFO] 页面类型: merchant
[2025-08-07 10:37:42] [INFO] 商户ID: '8034567958'
[2025-08-07 10:37:42] [INFO] 商品ID: ''
[2025-08-07 10:37:42] [INFO] 订单ID: ''
[2025-08-07 10:38:01] [INFO] === 新的访问开始 ===
[2025-08-07 10:38:01] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 10:38:01] [INFO] 请求方法: GET
[2025-08-07 10:38:01] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 10:38:01] [INFO] POST参数: []
[2025-08-07 10:38:01] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 10:38:01] [INFO] 客户端IP: **************
[2025-08-07 10:38:01] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 10:38:01] [INFO] 页面类型: merchant
[2025-08-07 10:38:01] [INFO] 商户ID: '8034567958'
[2025-08-07 10:38:01] [INFO] 商品ID: ''
[2025-08-07 10:38:01] [INFO] 订单ID: ''
[2025-08-07 10:38:52] [INFO] === 新的访问开始 ===
[2025-08-07 10:38:52] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 10:38:52] [INFO] 请求方法: GET
[2025-08-07 10:38:52] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 10:38:52] [INFO] POST参数: []
[2025-08-07 10:38:52] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 10:38:52] [INFO] 客户端IP: **************
[2025-08-07 10:38:52] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 10:38:52] [INFO] 页面类型: merchant
[2025-08-07 10:38:52] [INFO] 商户ID: '8034567958'
[2025-08-07 10:38:52] [INFO] 商品ID: ''
[2025-08-07 10:38:52] [INFO] 订单ID: ''
[2025-08-07 10:39:04] [INFO] === 新的访问开始 ===
[2025-08-07 10:39:04] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 10:39:04] [INFO] 请求方法: GET
[2025-08-07 10:39:04] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 10:39:04] [INFO] POST参数: []
[2025-08-07 10:39:04] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 10:39:04] [INFO] 客户端IP: **************
[2025-08-07 10:39:04] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 10:39:04] [INFO] 页面类型: merchant
[2025-08-07 10:39:04] [INFO] 商户ID: '8034567958'
[2025-08-07 10:39:04] [INFO] 商品ID: ''
[2025-08-07 10:39:04] [INFO] 订单ID: ''
[2025-08-07 10:45:04] [INFO] === 新的访问开始 ===
[2025-08-07 10:45:04] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 10:45:04] [INFO] 请求方法: GET
[2025-08-07 10:45:04] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 10:45:04] [INFO] POST参数: []
[2025-08-07 10:45:04] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 10:45:04] [INFO] 客户端IP: **************
[2025-08-07 10:45:04] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 10:45:04] [INFO] 页面类型: merchant
[2025-08-07 10:45:04] [INFO] 商户ID: '8034567958'
[2025-08-07 10:45:04] [INFO] 商品ID: ''
[2025-08-07 10:45:04] [INFO] 订单ID: ''
[2025-08-07 10:46:21] [INFO] === 新的访问开始 ===
[2025-08-07 10:46:21] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 10:46:21] [INFO] 请求方法: GET
[2025-08-07 10:46:21] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 10:46:21] [INFO] POST参数: []
[2025-08-07 10:46:21] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 10:46:21] [INFO] 客户端IP: **************
[2025-08-07 10:46:21] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 10:46:21] [INFO] 页面类型: merchant
[2025-08-07 10:46:21] [INFO] 商户ID: '8034567958'
[2025-08-07 10:46:21] [INFO] 商品ID: ''
[2025-08-07 10:46:21] [INFO] 订单ID: ''
[2025-08-07 11:00:14] [INFO] === 新的访问开始 ===
[2025-08-07 11:00:14] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 11:00:14] [INFO] 请求方法: GET
[2025-08-07 11:00:14] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 11:00:14] [INFO] POST参数: []
[2025-08-07 11:00:14] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 11:00:14] [INFO] 客户端IP: **************
[2025-08-07 11:00:14] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 11:00:14] [INFO] 页面类型: merchant
[2025-08-07 11:00:14] [INFO] 商户ID: '8034567958'
[2025-08-07 11:00:14] [INFO] 商品ID: ''
[2025-08-07 11:00:14] [INFO] 订单ID: ''
[2025-08-07 11:05:28] [INFO] === 新的访问开始 ===
[2025-08-07 11:05:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 11:05:28] [INFO] 请求方法: GET
[2025-08-07 11:05:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 11:05:28] [INFO] POST参数: []
[2025-08-07 11:05:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-07 11:05:28] [INFO] 客户端IP: **************
[2025-08-07 11:05:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 11:05:28] [INFO] 页面类型: merchant
[2025-08-07 11:05:28] [INFO] 商户ID: '8034567958'
[2025-08-07 11:05:28] [INFO] 商品ID: ''
[2025-08-07 11:05:28] [INFO] 订单ID: ''
[2025-08-07 11:32:46] [INFO] === 新的访问开始 ===
[2025-08-07 11:32:46] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 11:32:46] [INFO] 请求方法: GET
[2025-08-07 11:32:46] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 11:32:46] [INFO] POST参数: []
[2025-08-07 11:32:46] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 11:32:46] [INFO] 客户端IP: *************
[2025-08-07 11:32:46] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 11:32:46] [INFO] 页面类型: merchant
[2025-08-07 11:32:46] [INFO] 商户ID: '8034567958'
[2025-08-07 11:32:46] [INFO] 商品ID: ''
[2025-08-07 11:32:46] [INFO] 订单ID: ''
[2025-08-07 11:33:42] [INFO] === 新的访问开始 ===
[2025-08-07 11:33:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 11:33:42] [INFO] 请求方法: GET
[2025-08-07 11:33:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 11:33:42] [INFO] POST参数: []
[2025-08-07 11:33:42] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 11:33:42] [INFO] 客户端IP: *************
[2025-08-07 11:33:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 11:33:42] [INFO] 页面类型: merchant
[2025-08-07 11:33:42] [INFO] 商户ID: '8034567958'
[2025-08-07 11:33:42] [INFO] 商品ID: ''
[2025-08-07 11:33:42] [INFO] 订单ID: ''
[2025-08-07 11:34:08] [INFO] === 新的访问开始 ===
[2025-08-07 11:34:08] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 11:34:08] [INFO] 请求方法: GET
[2025-08-07 11:34:08] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 11:34:08] [INFO] POST参数: []
[2025-08-07 11:34:08] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-07 11:34:08] [INFO] 客户端IP: *************
[2025-08-07 11:34:08] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 11:34:08] [INFO] 页面类型: merchant
[2025-08-07 11:34:08] [INFO] 商户ID: '8034567958'
[2025-08-07 11:34:08] [INFO] 商品ID: ''
[2025-08-07 11:34:08] [INFO] 订单ID: ''
[2025-08-07 11:47:26] [INFO] === 新的访问开始 ===
[2025-08-07 11:47:26] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 11:47:26] [INFO] 请求方法: GET
[2025-08-07 11:47:26] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 11:47:26] [INFO] POST参数: []
[2025-08-07 11:47:26] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-07 11:47:26] [INFO] 客户端IP: **************
[2025-08-07 11:47:26] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 11:47:26] [INFO] 页面类型: merchant
[2025-08-07 11:47:26] [INFO] 商户ID: '8034567958'
[2025-08-07 11:47:26] [INFO] 商品ID: ''
[2025-08-07 11:47:26] [INFO] 订单ID: ''
[2025-08-07 11:47:35] [INFO] === 新的访问开始 ===
[2025-08-07 11:47:35] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 11:47:35] [INFO] 请求方法: GET
[2025-08-07 11:47:35] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 11:47:35] [INFO] POST参数: []
[2025-08-07 11:47:35] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 13; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36
[2025-08-07 11:47:35] [INFO] 客户端IP: **************
[2025-08-07 11:47:35] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 11:47:35] [INFO] 页面类型: merchant
[2025-08-07 11:47:35] [INFO] 商户ID: '8034567958'
[2025-08-07 11:47:35] [INFO] 商品ID: ''
[2025-08-07 11:47:35] [INFO] 订单ID: ''
[2025-08-07 12:23:28] [INFO] === 新的访问开始 ===
[2025-08-07 12:23:28] [INFO] 请求URI: /shop.php?sp=23
[2025-08-07 12:23:28] [INFO] 请求方法: GET
[2025-08-07 12:23:28] [INFO] GET参数: {"sp":"23"}
[2025-08-07 12:23:28] [INFO] POST参数: []
[2025-08-07 12:23:28] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:23:28] [INFO] 客户端IP: **************
[2025-08-07 12:23:28] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-07 12:23:28] [INFO] 页面类型: product
[2025-08-07 12:23:28] [INFO] 商户ID: ''
[2025-08-07 12:23:28] [INFO] 商品ID: '23'
[2025-08-07 12:23:28] [INFO] 订单ID: ''
[2025-08-07 12:23:48] [INFO] === 新的访问开始 ===
[2025-08-07 12:23:48] [INFO] 请求URI: /shop.php?dd=123456
[2025-08-07 12:23:48] [INFO] 请求方法: GET
[2025-08-07 12:23:48] [INFO] GET参数: {"dd":"123456"}
[2025-08-07 12:23:48] [INFO] POST参数: []
[2025-08-07 12:23:48] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:23:48] [INFO] 客户端IP: **************
[2025-08-07 12:23:48] [INFO] 解析参数 - sj: '', sp: '', dd: '123456'
[2025-08-07 12:23:48] [INFO] 页面类型: order
[2025-08-07 12:23:48] [INFO] 商户ID: ''
[2025-08-07 12:23:48] [INFO] 商品ID: ''
[2025-08-07 12:23:48] [INFO] 订单ID: '123456'
[2025-08-07 12:23:49] [INFO] === 新的访问开始 ===
[2025-08-07 12:23:49] [INFO] 请求URI: /shop.php
[2025-08-07 12:23:49] [INFO] 请求方法: GET
[2025-08-07 12:23:49] [INFO] GET参数: []
[2025-08-07 12:23:49] [INFO] POST参数: []
[2025-08-07 12:23:49] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; HUAWEI P30 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36
[2025-08-07 12:23:49] [INFO] 客户端IP: ************
[2025-08-07 12:23:49] [INFO] 解析参数 - sj: '', sp: '', dd: ''
[2025-08-07 12:23:49] [ERROR] 参数验证失败：sj、sp和dd都为空，返回404
[2025-08-07 12:23:55] [INFO] === 新的访问开始 ===
[2025-08-07 12:23:55] [INFO] 请求URI: /shop.php?dd=12345678
[2025-08-07 12:23:55] [INFO] 请求方法: GET
[2025-08-07 12:23:55] [INFO] GET参数: {"dd":"12345678"}
[2025-08-07 12:23:55] [INFO] POST参数: []
[2025-08-07 12:23:55] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:23:55] [INFO] 客户端IP: **************
[2025-08-07 12:23:55] [INFO] 解析参数 - sj: '', sp: '', dd: '12345678'
[2025-08-07 12:23:55] [INFO] 页面类型: order
[2025-08-07 12:23:55] [INFO] 商户ID: ''
[2025-08-07 12:23:55] [INFO] 商品ID: ''
[2025-08-07 12:23:55] [INFO] 订单ID: '12345678'
[2025-08-07 12:23:58] [INFO] === 新的访问开始 ===
[2025-08-07 12:23:58] [INFO] 请求URI: /shop.php?sp=23
[2025-08-07 12:23:58] [INFO] 请求方法: GET
[2025-08-07 12:23:58] [INFO] GET参数: {"sp":"23"}
[2025-08-07 12:23:58] [INFO] POST参数: []
[2025-08-07 12:23:58] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:23:58] [INFO] 客户端IP: **************
[2025-08-07 12:23:58] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-07 12:23:58] [INFO] 页面类型: product
[2025-08-07 12:23:58] [INFO] 商户ID: ''
[2025-08-07 12:23:58] [INFO] 商品ID: '23'
[2025-08-07 12:23:58] [INFO] 订单ID: ''
[2025-08-07 12:24:05] [INFO] === 新的访问开始 ===
[2025-08-07 12:24:05] [INFO] 请求URI: /shop.php?dd=O
[2025-08-07 12:24:05] [INFO] 请求方法: GET
[2025-08-07 12:24:05] [INFO] GET参数: {"dd":"O"}
[2025-08-07 12:24:05] [INFO] POST参数: []
[2025-08-07 12:24:05] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:24:05] [INFO] 客户端IP: **************
[2025-08-07 12:24:05] [INFO] 解析参数 - sj: '', sp: '', dd: 'O'
[2025-08-07 12:24:05] [INFO] 页面类型: order
[2025-08-07 12:24:05] [INFO] 商户ID: ''
[2025-08-07 12:24:05] [INFO] 商品ID: ''
[2025-08-07 12:24:05] [INFO] 订单ID: 'O'
[2025-08-07 12:24:16] [INFO] === 新的访问开始 ===
[2025-08-07 12:24:16] [INFO] 请求URI: /shop.php?sp=23
[2025-08-07 12:24:16] [INFO] 请求方法: GET
[2025-08-07 12:24:16] [INFO] GET参数: {"sp":"23"}
[2025-08-07 12:24:16] [INFO] POST参数: []
[2025-08-07 12:24:16] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:24:16] [INFO] 客户端IP: **************
[2025-08-07 12:24:16] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-07 12:24:16] [INFO] 页面类型: product
[2025-08-07 12:24:16] [INFO] 商户ID: ''
[2025-08-07 12:24:16] [INFO] 商品ID: '23'
[2025-08-07 12:24:16] [INFO] 订单ID: ''
[2025-08-07 12:24:19] [INFO] === 新的访问开始 ===
[2025-08-07 12:24:19] [INFO] 请求URI: /shop.php?sp=23
[2025-08-07 12:24:19] [INFO] 请求方法: GET
[2025-08-07 12:24:19] [INFO] GET参数: {"sp":"23"}
[2025-08-07 12:24:19] [INFO] POST参数: []
[2025-08-07 12:24:19] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:24:19] [INFO] 客户端IP: **************
[2025-08-07 12:24:19] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-07 12:24:19] [INFO] 页面类型: product
[2025-08-07 12:24:19] [INFO] 商户ID: ''
[2025-08-07 12:24:19] [INFO] 商品ID: '23'
[2025-08-07 12:24:19] [INFO] 订单ID: ''
[2025-08-07 12:24:27] [INFO] === 新的访问开始 ===
[2025-08-07 12:24:27] [INFO] 请求URI: /shop.php?sp=22
[2025-08-07 12:24:27] [INFO] 请求方法: GET
[2025-08-07 12:24:27] [INFO] GET参数: {"sp":"22"}
[2025-08-07 12:24:27] [INFO] POST参数: []
[2025-08-07 12:24:27] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:24:27] [INFO] 客户端IP: **************
[2025-08-07 12:24:27] [INFO] 解析参数 - sj: '', sp: '22', dd: ''
[2025-08-07 12:24:27] [INFO] 页面类型: product
[2025-08-07 12:24:27] [INFO] 商户ID: ''
[2025-08-07 12:24:27] [INFO] 商品ID: '22'
[2025-08-07 12:24:27] [INFO] 订单ID: ''
[2025-08-07 12:24:40] [INFO] === 新的访问开始 ===
[2025-08-07 12:24:40] [INFO] 请求URI: /shop.php?sp=1
[2025-08-07 12:24:40] [INFO] 请求方法: GET
[2025-08-07 12:24:40] [INFO] GET参数: {"sp":"1"}
[2025-08-07 12:24:40] [INFO] POST参数: []
[2025-08-07 12:24:40] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:24:40] [INFO] 客户端IP: **************
[2025-08-07 12:24:40] [INFO] 解析参数 - sj: '', sp: '1', dd: ''
[2025-08-07 12:24:40] [INFO] 页面类型: product
[2025-08-07 12:24:40] [INFO] 商户ID: ''
[2025-08-07 12:24:40] [INFO] 商品ID: '1'
[2025-08-07 12:24:40] [INFO] 订单ID: ''
[2025-08-07 12:24:46] [INFO] === 新的访问开始 ===
[2025-08-07 12:24:46] [INFO] 请求URI: /shop.php
[2025-08-07 12:24:46] [INFO] 请求方法: GET
[2025-08-07 12:24:46] [INFO] GET参数: []
[2025-08-07 12:24:46] [INFO] POST参数: []
[2025-08-07 12:24:46] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 12; HarmonyOS; TAS-AL00; HMSCore 6.13.0.339) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 12:24:46] [INFO] 客户端IP: **************
[2025-08-07 12:24:46] [INFO] 解析参数 - sj: '', sp: '', dd: ''
[2025-08-07 12:24:46] [ERROR] 参数验证失败：sj、sp和dd都为空，返回404
[2025-08-07 12:53:36] [INFO] === 新的访问开始 ===
[2025-08-07 12:53:36] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 12:53:36] [INFO] 请求方法: GET
[2025-08-07 12:53:36] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 12:53:36] [INFO] POST参数: []
[2025-08-07 12:53:36] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-07 12:53:36] [INFO] 客户端IP: ************
[2025-08-07 12:53:36] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 12:53:36] [INFO] 页面类型: merchant
[2025-08-07 12:53:36] [INFO] 商户ID: '8034567958'
[2025-08-07 12:53:36] [INFO] 商品ID: ''
[2025-08-07 12:53:36] [INFO] 订单ID: ''
[2025-08-07 12:56:51] [INFO] === 新的访问开始 ===
[2025-08-07 12:56:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 12:56:51] [INFO] 请求方法: GET
[2025-08-07 12:56:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 12:56:51] [INFO] POST参数: []
[2025-08-07 12:56:51] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-07 12:56:51] [INFO] 客户端IP: ************
[2025-08-07 12:56:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 12:56:51] [INFO] 页面类型: merchant
[2025-08-07 12:56:51] [INFO] 商户ID: '8034567958'
[2025-08-07 12:56:51] [INFO] 商品ID: ''
[2025-08-07 12:56:51] [INFO] 订单ID: ''
[2025-08-07 13:00:15] [INFO] === 新的访问开始 ===
[2025-08-07 13:00:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 13:00:15] [INFO] 请求方法: GET
[2025-08-07 13:00:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 13:00:15] [INFO] POST参数: []
[2025-08-07 13:00:15] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-07 13:00:15] [INFO] 客户端IP: **************
[2025-08-07 13:00:15] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 13:00:15] [INFO] 页面类型: merchant
[2025-08-07 13:00:15] [INFO] 商户ID: '8034567958'
[2025-08-07 13:00:15] [INFO] 商品ID: ''
[2025-08-07 13:00:15] [INFO] 订单ID: ''
[2025-08-07 13:14:55] [INFO] === 新的访问开始 ===
[2025-08-07 13:14:55] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 13:14:55] [INFO] 请求方法: GET
[2025-08-07 13:14:55] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 13:14:55] [INFO] POST参数: []
[2025-08-07 13:14:55] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; V2339FA) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-07 13:14:55] [INFO] 客户端IP: **************
[2025-08-07 13:14:55] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 13:14:55] [INFO] 页面类型: merchant
[2025-08-07 13:14:55] [INFO] 商户ID: '8034567958'
[2025-08-07 13:14:55] [INFO] 商品ID: ''
[2025-08-07 13:14:55] [INFO] 订单ID: ''
[2025-08-07 13:15:26] [INFO] === 新的访问开始 ===
[2025-08-07 13:15:26] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 13:15:26] [INFO] 请求方法: GET
[2025-08-07 13:15:26] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 13:15:26] [INFO] POST参数: []
[2025-08-07 13:15:26] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; V2339FA) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-07 13:15:26] [INFO] 客户端IP: **************
[2025-08-07 13:15:26] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 13:15:26] [INFO] 页面类型: merchant
[2025-08-07 13:15:26] [INFO] 商户ID: '8034567958'
[2025-08-07 13:15:26] [INFO] 商品ID: ''
[2025-08-07 13:15:26] [INFO] 订单ID: ''
[2025-08-07 13:35:22] [INFO] === 新的访问开始 ===
[2025-08-07 13:35:22] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 13:35:22] [INFO] 请求方法: GET
[2025-08-07 13:35:22] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 13:35:22] [INFO] POST参数: []
[2025-08-07 13:35:22] [INFO] User-Agent: Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Safari/537.36
[2025-08-07 13:35:22] [INFO] 客户端IP: **************
[2025-08-07 13:35:22] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 13:35:22] [INFO] 页面类型: merchant
[2025-08-07 13:35:22] [INFO] 商户ID: '8034567958'
[2025-08-07 13:35:22] [INFO] 商品ID: ''
[2025-08-07 13:35:22] [INFO] 订单ID: ''
[2025-08-07 14:53:07] [INFO] === 新的访问开始 ===
[2025-08-07 14:53:07] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 14:53:07] [INFO] 请求方法: GET
[2025-08-07 14:53:07] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 14:53:07] [INFO] POST参数: []
[2025-08-07 14:53:07] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 14:53:07] [INFO] 客户端IP: **************
[2025-08-07 14:53:07] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 14:53:07] [INFO] 页面类型: merchant
[2025-08-07 14:53:07] [INFO] 商户ID: '8034567958'
[2025-08-07 14:53:07] [INFO] 商品ID: ''
[2025-08-07 14:53:07] [INFO] 订单ID: ''
[2025-08-07 14:53:42] [INFO] === 新的访问开始 ===
[2025-08-07 14:53:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 14:53:42] [INFO] 请求方法: GET
[2025-08-07 14:53:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 14:53:42] [INFO] POST参数: []
[2025-08-07 14:53:42] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 14:53:42] [INFO] 客户端IP: **************
[2025-08-07 14:53:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 14:53:42] [INFO] 页面类型: merchant
[2025-08-07 14:53:42] [INFO] 商户ID: '8034567958'
[2025-08-07 14:53:42] [INFO] 商品ID: ''
[2025-08-07 14:53:42] [INFO] 订单ID: ''
[2025-08-07 14:54:57] [INFO] === 新的访问开始 ===
[2025-08-07 14:54:57] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 14:54:57] [INFO] 请求方法: GET
[2025-08-07 14:54:57] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 14:54:57] [INFO] POST参数: []
[2025-08-07 14:54:57] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 14:54:57] [INFO] 客户端IP: **************
[2025-08-07 14:54:57] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 14:54:57] [INFO] 页面类型: merchant
[2025-08-07 14:54:57] [INFO] 商户ID: '8034567958'
[2025-08-07 14:54:57] [INFO] 商品ID: ''
[2025-08-07 14:54:57] [INFO] 订单ID: ''
[2025-08-07 14:56:14] [INFO] === 新的访问开始 ===
[2025-08-07 14:56:14] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 14:56:14] [INFO] 请求方法: GET
[2025-08-07 14:56:14] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 14:56:14] [INFO] POST参数: []
[2025-08-07 14:56:14] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 14:56:14] [INFO] 客户端IP: **************
[2025-08-07 14:56:14] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 14:56:14] [INFO] 页面类型: merchant
[2025-08-07 14:56:14] [INFO] 商户ID: '8034567958'
[2025-08-07 14:56:14] [INFO] 商品ID: ''
[2025-08-07 14:56:14] [INFO] 订单ID: ''
[2025-08-07 14:57:29] [INFO] === 新的访问开始 ===
[2025-08-07 14:57:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 14:57:29] [INFO] 请求方法: GET
[2025-08-07 14:57:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 14:57:29] [INFO] POST参数: []
[2025-08-07 14:57:29] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 14:57:29] [INFO] 客户端IP: **************
[2025-08-07 14:57:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 14:57:29] [INFO] 页面类型: merchant
[2025-08-07 14:57:29] [INFO] 商户ID: '8034567958'
[2025-08-07 14:57:29] [INFO] 商品ID: ''
[2025-08-07 14:57:29] [INFO] 订单ID: ''
[2025-08-07 14:57:48] [INFO] === 新的访问开始 ===
[2025-08-07 14:57:48] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 14:57:48] [INFO] 请求方法: GET
[2025-08-07 14:57:48] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 14:57:48] [INFO] POST参数: []
[2025-08-07 14:57:48] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 14:57:48] [INFO] 客户端IP: **************
[2025-08-07 14:57:48] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 14:57:48] [INFO] 页面类型: merchant
[2025-08-07 14:57:48] [INFO] 商户ID: '8034567958'
[2025-08-07 14:57:48] [INFO] 商品ID: ''
[2025-08-07 14:57:48] [INFO] 订单ID: ''
[2025-08-07 14:59:03] [INFO] === 新的访问开始 ===
[2025-08-07 14:59:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 14:59:03] [INFO] 请求方法: GET
[2025-08-07 14:59:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 14:59:03] [INFO] POST参数: []
[2025-08-07 14:59:03] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 14:59:03] [INFO] 客户端IP: **************
[2025-08-07 14:59:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 14:59:03] [INFO] 页面类型: merchant
[2025-08-07 14:59:03] [INFO] 商户ID: '8034567958'
[2025-08-07 14:59:03] [INFO] 商品ID: ''
[2025-08-07 14:59:03] [INFO] 订单ID: ''
[2025-08-07 15:15:29] [INFO] === 新的访问开始 ===
[2025-08-07 15:15:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:15:29] [INFO] 请求方法: GET
[2025-08-07 15:15:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:15:29] [INFO] POST参数: []
[2025-08-07 15:15:29] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:15:29] [INFO] 客户端IP: **************
[2025-08-07 15:15:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:15:29] [INFO] 页面类型: merchant
[2025-08-07 15:15:29] [INFO] 商户ID: '8034567958'
[2025-08-07 15:15:29] [INFO] 商品ID: ''
[2025-08-07 15:15:29] [INFO] 订单ID: ''
[2025-08-07 15:16:24] [INFO] === 新的访问开始 ===
[2025-08-07 15:16:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:16:24] [INFO] 请求方法: GET
[2025-08-07 15:16:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:16:24] [INFO] POST参数: []
[2025-08-07 15:16:24] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:16:24] [INFO] 客户端IP: **************
[2025-08-07 15:16:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:16:24] [INFO] 页面类型: merchant
[2025-08-07 15:16:24] [INFO] 商户ID: '8034567958'
[2025-08-07 15:16:24] [INFO] 商品ID: ''
[2025-08-07 15:16:24] [INFO] 订单ID: ''
[2025-08-07 15:17:12] [INFO] === 新的访问开始 ===
[2025-08-07 15:17:12] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:17:12] [INFO] 请求方法: GET
[2025-08-07 15:17:12] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:17:12] [INFO] POST参数: []
[2025-08-07 15:17:12] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:17:12] [INFO] 客户端IP: **************
[2025-08-07 15:17:12] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:17:12] [INFO] 页面类型: merchant
[2025-08-07 15:17:12] [INFO] 商户ID: '8034567958'
[2025-08-07 15:17:12] [INFO] 商品ID: ''
[2025-08-07 15:17:12] [INFO] 订单ID: ''
[2025-08-07 15:20:55] [INFO] === 新的访问开始 ===
[2025-08-07 15:20:55] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:20:55] [INFO] 请求方法: GET
[2025-08-07 15:20:55] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:20:55] [INFO] POST参数: []
[2025-08-07 15:20:55] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:20:55] [INFO] 客户端IP: ************
[2025-08-07 15:20:55] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:20:55] [INFO] 页面类型: merchant
[2025-08-07 15:20:55] [INFO] 商户ID: '8034567958'
[2025-08-07 15:20:55] [INFO] 商品ID: ''
[2025-08-07 15:20:55] [INFO] 订单ID: ''
[2025-08-07 15:22:37] [INFO] === 新的访问开始 ===
[2025-08-07 15:22:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:22:37] [INFO] 请求方法: GET
[2025-08-07 15:22:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:22:37] [INFO] POST参数: []
[2025-08-07 15:22:37] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:22:37] [INFO] 客户端IP: *************
[2025-08-07 15:22:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:22:37] [INFO] 页面类型: merchant
[2025-08-07 15:22:37] [INFO] 商户ID: '8034567958'
[2025-08-07 15:22:37] [INFO] 商品ID: ''
[2025-08-07 15:22:37] [INFO] 订单ID: ''
[2025-08-07 15:23:10] [INFO] === 新的访问开始 ===
[2025-08-07 15:23:10] [INFO] 请求URI: /shop.php?dd=2025080715172537835
[2025-08-07 15:23:10] [INFO] 请求方法: GET
[2025-08-07 15:23:10] [INFO] GET参数: {"dd":"2025080715172537835"}
[2025-08-07 15:23:10] [INFO] POST参数: []
[2025-08-07 15:23:10] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:23:10] [INFO] 客户端IP: *************
[2025-08-07 15:23:10] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080715172537835'
[2025-08-07 15:23:10] [INFO] 页面类型: order
[2025-08-07 15:23:10] [INFO] 商户ID: ''
[2025-08-07 15:23:10] [INFO] 商品ID: ''
[2025-08-07 15:23:10] [INFO] 订单ID: '2025080715172537835'
[2025-08-07 15:23:29] [INFO] === 新的访问开始 ===
[2025-08-07 15:23:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:23:29] [INFO] 请求方法: GET
[2025-08-07 15:23:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:23:29] [INFO] POST参数: []
[2025-08-07 15:23:29] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:23:29] [INFO] 客户端IP: *************
[2025-08-07 15:23:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:23:29] [INFO] 页面类型: merchant
[2025-08-07 15:23:29] [INFO] 商户ID: '8034567958'
[2025-08-07 15:23:29] [INFO] 商品ID: ''
[2025-08-07 15:23:29] [INFO] 订单ID: ''
[2025-08-07 15:24:52] [INFO] === 新的访问开始 ===
[2025-08-07 15:24:52] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:24:52] [INFO] 请求方法: GET
[2025-08-07 15:24:52] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:24:52] [INFO] POST参数: []
[2025-08-07 15:24:52] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:24:52] [INFO] 客户端IP: ************
[2025-08-07 15:24:52] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:24:52] [INFO] 页面类型: merchant
[2025-08-07 15:24:52] [INFO] 商户ID: '8034567958'
[2025-08-07 15:24:52] [INFO] 商品ID: ''
[2025-08-07 15:24:52] [INFO] 订单ID: ''
[2025-08-07 15:27:50] [INFO] === 新的访问开始 ===
[2025-08-07 15:27:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:27:50] [INFO] 请求方法: GET
[2025-08-07 15:27:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:27:50] [INFO] POST参数: []
[2025-08-07 15:27:50] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:27:50] [INFO] 客户端IP: ************
[2025-08-07 15:27:50] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:27:50] [INFO] 页面类型: merchant
[2025-08-07 15:27:50] [INFO] 商户ID: '8034567958'
[2025-08-07 15:27:50] [INFO] 商品ID: ''
[2025-08-07 15:27:50] [INFO] 订单ID: ''
[2025-08-07 15:35:58] [INFO] === 新的访问开始 ===
[2025-08-07 15:35:58] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:35:58] [INFO] 请求方法: GET
[2025-08-07 15:35:58] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:35:58] [INFO] POST参数: []
[2025-08-07 15:35:58] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 15:35:58] [INFO] 客户端IP: **************
[2025-08-07 15:35:58] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:35:58] [INFO] 页面类型: merchant
[2025-08-07 15:35:58] [INFO] 商户ID: '8034567958'
[2025-08-07 15:35:58] [INFO] 商品ID: ''
[2025-08-07 15:35:58] [INFO] 订单ID: ''
[2025-08-07 15:46:57] [INFO] === 新的访问开始 ===
[2025-08-07 15:46:57] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:46:57] [INFO] 请求方法: GET
[2025-08-07 15:46:57] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:46:57] [INFO] POST参数: []
[2025-08-07 15:46:57] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-07 15:46:57] [INFO] 客户端IP: **************
[2025-08-07 15:46:57] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:46:57] [INFO] 页面类型: merchant
[2025-08-07 15:46:57] [INFO] 商户ID: '8034567958'
[2025-08-07 15:46:57] [INFO] 商品ID: ''
[2025-08-07 15:46:57] [INFO] 订单ID: ''
[2025-08-07 15:47:34] [INFO] === 新的访问开始 ===
[2025-08-07 15:47:34] [INFO] 请求URI: /shop.php?dd=ORDER17545266126907
[2025-08-07 15:47:34] [INFO] 请求方法: GET
[2025-08-07 15:47:34] [INFO] GET参数: {"dd":"ORDER17545266126907"}
[2025-08-07 15:47:34] [INFO] POST参数: []
[2025-08-07 15:47:34] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-07 15:47:34] [INFO] 客户端IP: **************
[2025-08-07 15:47:34] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17545266126907'
[2025-08-07 15:47:34] [INFO] 页面类型: order
[2025-08-07 15:47:34] [INFO] 商户ID: ''
[2025-08-07 15:47:34] [INFO] 商品ID: ''
[2025-08-07 15:47:34] [INFO] 订单ID: 'ORDER17545266126907'
[2025-08-07 15:50:59] [INFO] === 新的访问开始 ===
[2025-08-07 15:50:59] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 15:50:59] [INFO] 请求方法: GET
[2025-08-07 15:50:59] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 15:50:59] [INFO] POST参数: []
[2025-08-07 15:50:59] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 9; V1814A Build/PKQ1.180819.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.6312.118 Mobile Safari/537.36 VivoBrowser/********
[2025-08-07 15:50:59] [INFO] 客户端IP: **************
[2025-08-07 15:50:59] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 15:50:59] [INFO] 页面类型: merchant
[2025-08-07 15:50:59] [INFO] 商户ID: '8034567958'
[2025-08-07 15:50:59] [INFO] 商品ID: ''
[2025-08-07 15:50:59] [INFO] 订单ID: ''
[2025-08-07 16:02:29] [INFO] === 新的访问开始 ===
[2025-08-07 16:02:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 16:02:29] [INFO] 请求方法: GET
[2025-08-07 16:02:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 16:02:29] [INFO] POST参数: []
[2025-08-07 16:02:29] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:02:29] [INFO] 客户端IP: ************
[2025-08-07 16:02:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 16:02:29] [INFO] 页面类型: merchant
[2025-08-07 16:02:29] [INFO] 商户ID: '8034567958'
[2025-08-07 16:02:29] [INFO] 商品ID: ''
[2025-08-07 16:02:29] [INFO] 订单ID: ''
[2025-08-07 16:02:43] [INFO] === 新的访问开始 ===
[2025-08-07 16:02:43] [INFO] 请求URI: /shop.php?dd=ORDER17540294861576
[2025-08-07 16:02:43] [INFO] 请求方法: GET
[2025-08-07 16:02:43] [INFO] GET参数: {"dd":"ORDER17540294861576"}
[2025-08-07 16:02:43] [INFO] POST参数: []
[2025-08-07 16:02:43] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:02:43] [INFO] 客户端IP: ************
[2025-08-07 16:02:43] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17540294861576'
[2025-08-07 16:02:43] [INFO] 页面类型: order
[2025-08-07 16:02:43] [INFO] 商户ID: ''
[2025-08-07 16:02:43] [INFO] 商品ID: ''
[2025-08-07 16:02:43] [INFO] 订单ID: 'ORDER17540294861576'
[2025-08-07 16:02:58] [INFO] === 新的访问开始 ===
[2025-08-07 16:02:58] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 16:02:58] [INFO] 请求方法: GET
[2025-08-07 16:02:58] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 16:02:58] [INFO] POST参数: []
[2025-08-07 16:02:58] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:02:58] [INFO] 客户端IP: ************
[2025-08-07 16:02:58] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 16:02:58] [INFO] 页面类型: merchant
[2025-08-07 16:02:58] [INFO] 商户ID: '8034567958'
[2025-08-07 16:02:58] [INFO] 商品ID: ''
[2025-08-07 16:02:58] [INFO] 订单ID: ''
[2025-08-07 16:03:08] [INFO] === 新的访问开始 ===
[2025-08-07 16:03:08] [INFO] 请求URI: /shop.php?dd=ORDER17540294861576
[2025-08-07 16:03:08] [INFO] 请求方法: GET
[2025-08-07 16:03:08] [INFO] GET参数: {"dd":"ORDER17540294861576"}
[2025-08-07 16:03:08] [INFO] POST参数: []
[2025-08-07 16:03:08] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:03:08] [INFO] 客户端IP: ************
[2025-08-07 16:03:08] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17540294861576'
[2025-08-07 16:03:08] [INFO] 页面类型: order
[2025-08-07 16:03:08] [INFO] 商户ID: ''
[2025-08-07 16:03:08] [INFO] 商品ID: ''
[2025-08-07 16:03:08] [INFO] 订单ID: 'ORDER17540294861576'
[2025-08-07 16:04:44] [INFO] === 新的访问开始 ===
[2025-08-07 16:04:44] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 16:04:44] [INFO] 请求方法: GET
[2025-08-07 16:04:44] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 16:04:44] [INFO] POST参数: []
[2025-08-07 16:04:44] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:04:44] [INFO] 客户端IP: ************
[2025-08-07 16:04:44] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 16:04:44] [INFO] 页面类型: merchant
[2025-08-07 16:04:44] [INFO] 商户ID: '8034567958'
[2025-08-07 16:04:44] [INFO] 商品ID: ''
[2025-08-07 16:04:44] [INFO] 订单ID: ''
[2025-08-07 16:04:52] [INFO] === 新的访问开始 ===
[2025-08-07 16:04:52] [INFO] 请求URI: /shop.php?dd=ORDER17540294861576
[2025-08-07 16:04:52] [INFO] 请求方法: GET
[2025-08-07 16:04:52] [INFO] GET参数: {"dd":"ORDER17540294861576"}
[2025-08-07 16:04:52] [INFO] POST参数: []
[2025-08-07 16:04:52] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:04:52] [INFO] 客户端IP: ************
[2025-08-07 16:04:52] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17540294861576'
[2025-08-07 16:04:52] [INFO] 页面类型: order
[2025-08-07 16:04:52] [INFO] 商户ID: ''
[2025-08-07 16:04:52] [INFO] 商品ID: ''
[2025-08-07 16:04:52] [INFO] 订单ID: 'ORDER17540294861576'
[2025-08-07 16:05:02] [INFO] === 新的访问开始 ===
[2025-08-07 16:05:02] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 16:05:02] [INFO] 请求方法: GET
[2025-08-07 16:05:02] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 16:05:02] [INFO] POST参数: []
[2025-08-07 16:05:02] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:05:02] [INFO] 客户端IP: ************
[2025-08-07 16:05:02] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 16:05:02] [INFO] 页面类型: merchant
[2025-08-07 16:05:02] [INFO] 商户ID: '8034567958'
[2025-08-07 16:05:02] [INFO] 商品ID: ''
[2025-08-07 16:05:02] [INFO] 订单ID: ''
[2025-08-07 16:05:08] [INFO] === 新的访问开始 ===
[2025-08-07 16:05:08] [INFO] 请求URI: /shop.php?dd=ORDER17540294861576
[2025-08-07 16:05:08] [INFO] 请求方法: GET
[2025-08-07 16:05:08] [INFO] GET参数: {"dd":"ORDER17540294861576"}
[2025-08-07 16:05:08] [INFO] POST参数: []
[2025-08-07 16:05:08] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:05:08] [INFO] 客户端IP: ************
[2025-08-07 16:05:08] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17540294861576'
[2025-08-07 16:05:08] [INFO] 页面类型: order
[2025-08-07 16:05:08] [INFO] 商户ID: ''
[2025-08-07 16:05:08] [INFO] 商品ID: ''
[2025-08-07 16:05:08] [INFO] 订单ID: 'ORDER17540294861576'
[2025-08-07 16:08:16] [INFO] === 新的访问开始 ===
[2025-08-07 16:08:16] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 16:08:16] [INFO] 请求方法: GET
[2025-08-07 16:08:16] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 16:08:16] [INFO] POST参数: []
[2025-08-07 16:08:16] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:08:16] [INFO] 客户端IP: **************
[2025-08-07 16:08:16] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 16:08:16] [INFO] 页面类型: merchant
[2025-08-07 16:08:16] [INFO] 商户ID: '8034567958'
[2025-08-07 16:08:16] [INFO] 商品ID: ''
[2025-08-07 16:08:16] [INFO] 订单ID: ''
[2025-08-07 16:09:49] [INFO] === 新的访问开始 ===
[2025-08-07 16:09:49] [INFO] 请求URI: /shop.php?dd=1001270706525219
[2025-08-07 16:09:49] [INFO] 请求方法: GET
[2025-08-07 16:09:49] [INFO] GET参数: {"dd":"1001270706525219"}
[2025-08-07 16:09:49] [INFO] POST参数: []
[2025-08-07 16:09:49] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 16:09:49] [INFO] 客户端IP: **************
[2025-08-07 16:09:49] [INFO] 解析参数 - sj: '', sp: '', dd: '1001270706525219'
[2025-08-07 16:09:49] [INFO] 页面类型: order
[2025-08-07 16:09:49] [INFO] 商户ID: ''
[2025-08-07 16:09:49] [INFO] 商品ID: ''
[2025-08-07 16:09:49] [INFO] 订单ID: '1001270706525219'
[2025-08-07 16:20:02] [INFO] === 新的访问开始 ===
[2025-08-07 16:20:02] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 16:20:02] [INFO] 请求方法: GET
[2025-08-07 16:20:02] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 16:20:02] [INFO] POST参数: []
[2025-08-07 16:20:02] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-07 16:20:02] [INFO] 客户端IP: ************
[2025-08-07 16:20:02] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 16:20:02] [INFO] 页面类型: merchant
[2025-08-07 16:20:02] [INFO] 商户ID: '8034567958'
[2025-08-07 16:20:02] [INFO] 商品ID: ''
[2025-08-07 16:20:02] [INFO] 订单ID: ''
[2025-08-07 17:25:08] [INFO] === 新的访问开始 ===
[2025-08-07 17:25:08] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 17:25:08] [INFO] 请求方法: GET
[2025-08-07 17:25:08] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 17:25:08] [INFO] POST参数: []
[2025-08-07 17:25:08] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 17:25:08] [INFO] 客户端IP: **************
[2025-08-07 17:25:08] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 17:25:08] [INFO] 页面类型: merchant
[2025-08-07 17:25:08] [INFO] 商户ID: '8034567958'
[2025-08-07 17:25:08] [INFO] 商品ID: ''
[2025-08-07 17:25:08] [INFO] 订单ID: ''
[2025-08-07 17:26:26] [INFO] === 新的访问开始 ===
[2025-08-07 17:26:26] [INFO] 请求URI: /shop.php?dd=ORDER17540294861576
[2025-08-07 17:26:26] [INFO] 请求方法: GET
[2025-08-07 17:26:26] [INFO] GET参数: {"dd":"ORDER17540294861576"}
[2025-08-07 17:26:26] [INFO] POST参数: []
[2025-08-07 17:26:26] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 17:26:26] [INFO] 客户端IP: **************
[2025-08-07 17:26:26] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17540294861576'
[2025-08-07 17:26:26] [INFO] 页面类型: order
[2025-08-07 17:26:26] [INFO] 商户ID: ''
[2025-08-07 17:26:26] [INFO] 商品ID: ''
[2025-08-07 17:26:26] [INFO] 订单ID: 'ORDER17540294861576'
[2025-08-07 17:26:47] [INFO] === 新的访问开始 ===
[2025-08-07 17:26:47] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 17:26:47] [INFO] 请求方法: GET
[2025-08-07 17:26:47] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 17:26:47] [INFO] POST参数: []
[2025-08-07 17:26:47] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 17:26:47] [INFO] 客户端IP: **************
[2025-08-07 17:26:47] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 17:26:47] [INFO] 页面类型: merchant
[2025-08-07 17:26:47] [INFO] 商户ID: '8034567958'
[2025-08-07 17:26:47] [INFO] 商品ID: ''
[2025-08-07 17:26:47] [INFO] 订单ID: ''
[2025-08-07 17:26:52] [INFO] === 新的访问开始 ===
[2025-08-07 17:26:52] [INFO] 请求URI: /shop.php?dd=ORDER17540294861576
[2025-08-07 17:26:52] [INFO] 请求方法: GET
[2025-08-07 17:26:52] [INFO] GET参数: {"dd":"ORDER17540294861576"}
[2025-08-07 17:26:52] [INFO] POST参数: []
[2025-08-07 17:26:52] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.4.1 Mobile/22E252 Safari/604.1
[2025-08-07 17:26:52] [INFO] 客户端IP: **************
[2025-08-07 17:26:52] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17540294861576'
[2025-08-07 17:26:52] [INFO] 页面类型: order
[2025-08-07 17:26:52] [INFO] 商户ID: ''
[2025-08-07 17:26:52] [INFO] 商品ID: ''
[2025-08-07 17:26:52] [INFO] 订单ID: 'ORDER17540294861576'
[2025-08-07 17:32:12] [INFO] === 新的访问开始 ===
[2025-08-07 17:32:12] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 17:32:12] [INFO] 请求方法: GET
[2025-08-07 17:32:12] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 17:32:12] [INFO] POST参数: []
[2025-08-07 17:32:12] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; 24069RA21C Build/AQ3A.240912.001) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.119 Mobile Safari/537.36 XiaoMi/MiuiBrowser/20.0.20728
[2025-08-07 17:32:12] [INFO] 客户端IP: **************
[2025-08-07 17:32:12] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 17:32:12] [INFO] 页面类型: merchant
[2025-08-07 17:32:12] [INFO] 商户ID: '8034567958'
[2025-08-07 17:32:12] [INFO] 商品ID: ''
[2025-08-07 17:32:12] [INFO] 订单ID: ''
[2025-08-07 17:55:54] [INFO] === 新的访问开始 ===
[2025-08-07 17:55:54] [INFO] 请求URI: /shop.php?sp=23
[2025-08-07 17:55:54] [INFO] 请求方法: GET
[2025-08-07 17:55:54] [INFO] GET参数: {"sp":"23"}
[2025-08-07 17:55:54] [INFO] POST参数: []
[2025-08-07 17:55:54] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2425A) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-07 17:55:54] [INFO] 客户端IP: **************
[2025-08-07 17:55:54] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-07 17:55:54] [INFO] 页面类型: product
[2025-08-07 17:55:54] [INFO] 商户ID: ''
[2025-08-07 17:55:54] [INFO] 商品ID: '23'
[2025-08-07 17:55:54] [INFO] 订单ID: ''
[2025-08-07 20:38:31] [INFO] === 新的访问开始 ===
[2025-08-07 20:38:31] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 20:38:31] [INFO] 请求方法: GET
[2025-08-07 20:38:31] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 20:38:31] [INFO] POST参数: []
[2025-08-07 20:38:31] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.6478.71 Mobile Safari/537.36
[2025-08-07 20:38:31] [INFO] 客户端IP: **************
[2025-08-07 20:38:31] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 20:38:31] [INFO] 页面类型: merchant
[2025-08-07 20:38:31] [INFO] 商户ID: '8034567958'
[2025-08-07 20:38:31] [INFO] 商品ID: ''
[2025-08-07 20:38:31] [INFO] 订单ID: ''
[2025-08-07 21:12:33] [INFO] === 新的访问开始 ===
[2025-08-07 21:12:33] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 21:12:33] [INFO] 请求方法: GET
[2025-08-07 21:12:33] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 21:12:33] [INFO] POST参数: []
[2025-08-07 21:12:33] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_7_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.7.2 Mobile/21H221 Safari/604.1
[2025-08-07 21:12:33] [INFO] 客户端IP: *************
[2025-08-07 21:12:33] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 21:12:33] [INFO] 页面类型: merchant
[2025-08-07 21:12:33] [INFO] 商户ID: '8034567958'
[2025-08-07 21:12:33] [INFO] 商品ID: ''
[2025-08-07 21:12:33] [INFO] 订单ID: ''
[2025-08-07 21:44:33] [INFO] === 新的访问开始 ===
[2025-08-07 21:44:33] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 21:44:33] [INFO] 请求方法: GET
[2025-08-07 21:44:33] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 21:44:33] [INFO] POST参数: []
[2025-08-07 21:44:33] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; TYH612M; HMSCore 6.15.0.322) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 HuaweiBrowser/16.0.7.301 Mobile Safari/537.36
[2025-08-07 21:44:33] [INFO] 客户端IP: **************
[2025-08-07 21:44:33] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 21:44:33] [INFO] 页面类型: merchant
[2025-08-07 21:44:33] [INFO] 商户ID: '8034567958'
[2025-08-07 21:44:33] [INFO] 商品ID: ''
[2025-08-07 21:44:33] [INFO] 订单ID: ''
[2025-08-07 22:13:15] [INFO] === 新的访问开始 ===
[2025-08-07 22:13:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:13:15] [INFO] 请求方法: GET
[2025-08-07 22:13:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:13:15] [INFO] POST参数: []
[2025-08-07 22:13:15] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:13:15] [INFO] 客户端IP: **************
[2025-08-07 22:13:15] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:13:15] [INFO] 页面类型: merchant
[2025-08-07 22:13:15] [INFO] 商户ID: '8034567958'
[2025-08-07 22:13:15] [INFO] 商品ID: ''
[2025-08-07 22:13:15] [INFO] 订单ID: ''
[2025-08-07 22:14:10] [INFO] === 新的访问开始 ===
[2025-08-07 22:14:10] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:14:10] [INFO] 请求方法: GET
[2025-08-07 22:14:10] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:14:10] [INFO] POST参数: []
[2025-08-07 22:14:10] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:14:10] [INFO] 客户端IP: *************
[2025-08-07 22:14:10] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:14:10] [INFO] 页面类型: merchant
[2025-08-07 22:14:10] [INFO] 商户ID: '8034567958'
[2025-08-07 22:14:10] [INFO] 商品ID: ''
[2025-08-07 22:14:10] [INFO] 订单ID: ''
[2025-08-07 22:14:36] [INFO] === 新的访问开始 ===
[2025-08-07 22:14:36] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:14:36] [INFO] 请求方法: GET
[2025-08-07 22:14:36] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:14:36] [INFO] POST参数: []
[2025-08-07 22:14:36] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:14:36] [INFO] 客户端IP: **************
[2025-08-07 22:14:36] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:14:36] [INFO] 页面类型: merchant
[2025-08-07 22:14:36] [INFO] 商户ID: '8034567958'
[2025-08-07 22:14:36] [INFO] 商品ID: ''
[2025-08-07 22:14:36] [INFO] 订单ID: ''
[2025-08-07 22:16:56] [INFO] === 新的访问开始 ===
[2025-08-07 22:16:56] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:16:56] [INFO] 请求方法: GET
[2025-08-07 22:16:56] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:16:56] [INFO] POST参数: []
[2025-08-07 22:16:56] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:16:56] [INFO] 客户端IP: **************
[2025-08-07 22:16:56] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:16:56] [INFO] 页面类型: merchant
[2025-08-07 22:16:56] [INFO] 商户ID: '8034567958'
[2025-08-07 22:16:56] [INFO] 商品ID: ''
[2025-08-07 22:16:56] [INFO] 订单ID: ''
[2025-08-07 22:17:42] [INFO] === 新的访问开始 ===
[2025-08-07 22:17:42] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:17:42] [INFO] 请求方法: GET
[2025-08-07 22:17:42] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:17:42] [INFO] POST参数: []
[2025-08-07 22:17:42] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:17:42] [INFO] 客户端IP: **************
[2025-08-07 22:17:42] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:17:42] [INFO] 页面类型: merchant
[2025-08-07 22:17:42] [INFO] 商户ID: '8034567958'
[2025-08-07 22:17:42] [INFO] 商品ID: ''
[2025-08-07 22:17:42] [INFO] 订单ID: ''
[2025-08-07 22:18:51] [INFO] === 新的访问开始 ===
[2025-08-07 22:18:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:18:51] [INFO] 请求方法: GET
[2025-08-07 22:18:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:18:51] [INFO] POST参数: []
[2025-08-07 22:18:51] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:18:51] [INFO] 客户端IP: **************
[2025-08-07 22:18:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:18:51] [INFO] 页面类型: merchant
[2025-08-07 22:18:51] [INFO] 商户ID: '8034567958'
[2025-08-07 22:18:51] [INFO] 商品ID: ''
[2025-08-07 22:18:51] [INFO] 订单ID: ''
[2025-08-07 22:19:16] [INFO] === 新的访问开始 ===
[2025-08-07 22:19:16] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:19:16] [INFO] 请求方法: GET
[2025-08-07 22:19:16] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:19:16] [INFO] POST参数: []
[2025-08-07 22:19:16] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:19:16] [INFO] 客户端IP: **************
[2025-08-07 22:19:16] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:19:16] [INFO] 页面类型: merchant
[2025-08-07 22:19:16] [INFO] 商户ID: '8034567958'
[2025-08-07 22:19:16] [INFO] 商品ID: ''
[2025-08-07 22:19:16] [INFO] 订单ID: ''
[2025-08-07 22:19:53] [INFO] === 新的访问开始 ===
[2025-08-07 22:19:53] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:19:53] [INFO] 请求方法: GET
[2025-08-07 22:19:53] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:19:53] [INFO] POST参数: []
[2025-08-07 22:19:53] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:19:53] [INFO] 客户端IP: **************
[2025-08-07 22:19:53] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:19:53] [INFO] 页面类型: merchant
[2025-08-07 22:19:53] [INFO] 商户ID: '8034567958'
[2025-08-07 22:19:53] [INFO] 商品ID: ''
[2025-08-07 22:19:53] [INFO] 订单ID: ''
[2025-08-07 22:21:32] [INFO] === 新的访问开始 ===
[2025-08-07 22:21:32] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:21:32] [INFO] 请求方法: GET
[2025-08-07 22:21:32] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:21:32] [INFO] POST参数: []
[2025-08-07 22:21:32] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:21:32] [INFO] 客户端IP: **************
[2025-08-07 22:21:32] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:21:32] [INFO] 页面类型: merchant
[2025-08-07 22:21:32] [INFO] 商户ID: '8034567958'
[2025-08-07 22:21:32] [INFO] 商品ID: ''
[2025-08-07 22:21:32] [INFO] 订单ID: ''
[2025-08-07 22:21:56] [INFO] === 新的访问开始 ===
[2025-08-07 22:21:56] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:21:56] [INFO] 请求方法: GET
[2025-08-07 22:21:56] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:21:56] [INFO] POST参数: []
[2025-08-07 22:21:56] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:21:56] [INFO] 客户端IP: **************
[2025-08-07 22:21:56] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:21:56] [INFO] 页面类型: merchant
[2025-08-07 22:21:56] [INFO] 商户ID: '8034567958'
[2025-08-07 22:21:56] [INFO] 商品ID: ''
[2025-08-07 22:21:56] [INFO] 订单ID: ''
[2025-08-07 22:22:29] [INFO] === 新的访问开始 ===
[2025-08-07 22:22:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:22:29] [INFO] 请求方法: GET
[2025-08-07 22:22:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:22:29] [INFO] POST参数: []
[2025-08-07 22:22:29] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:22:29] [INFO] 客户端IP: **************
[2025-08-07 22:22:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:22:29] [INFO] 页面类型: merchant
[2025-08-07 22:22:29] [INFO] 商户ID: '8034567958'
[2025-08-07 22:22:29] [INFO] 商品ID: ''
[2025-08-07 22:22:29] [INFO] 订单ID: ''
[2025-08-07 22:22:35] [INFO] === 新的访问开始 ===
[2025-08-07 22:22:35] [INFO] 请求URI: /shop.php?dd=4200002791202508071956668773
[2025-08-07 22:22:35] [INFO] 请求方法: GET
[2025-08-07 22:22:35] [INFO] GET参数: {"dd":"4200002791202508071956668773"}
[2025-08-07 22:22:35] [INFO] POST参数: []
[2025-08-07 22:22:35] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:22:35] [INFO] 客户端IP: **************
[2025-08-07 22:22:35] [INFO] 解析参数 - sj: '', sp: '', dd: '4200002791202508071956668773'
[2025-08-07 22:22:35] [INFO] 页面类型: order
[2025-08-07 22:22:35] [INFO] 商户ID: ''
[2025-08-07 22:22:35] [INFO] 商品ID: ''
[2025-08-07 22:22:35] [INFO] 订单ID: '4200002791202508071956668773'
[2025-08-07 22:23:22] [INFO] === 新的访问开始 ===
[2025-08-07 22:23:22] [INFO] 请求URI: /shop.php?dd=1002173106525219
[2025-08-07 22:23:22] [INFO] 请求方法: GET
[2025-08-07 22:23:22] [INFO] GET参数: {"dd":"1002173106525219"}
[2025-08-07 22:23:22] [INFO] POST参数: []
[2025-08-07 22:23:22] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:23:22] [INFO] 客户端IP: **************
[2025-08-07 22:23:22] [INFO] 解析参数 - sj: '', sp: '', dd: '1002173106525219'
[2025-08-07 22:23:22] [INFO] 页面类型: order
[2025-08-07 22:23:22] [INFO] 商户ID: ''
[2025-08-07 22:23:22] [INFO] 商品ID: ''
[2025-08-07 22:23:22] [INFO] 订单ID: '1002173106525219'
[2025-08-07 22:24:17] [INFO] === 新的访问开始 ===
[2025-08-07 22:24:17] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:24:17] [INFO] 请求方法: GET
[2025-08-07 22:24:17] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:24:17] [INFO] POST参数: []
[2025-08-07 22:24:17] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:24:17] [INFO] 客户端IP: **************
[2025-08-07 22:24:17] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:24:17] [INFO] 页面类型: merchant
[2025-08-07 22:24:17] [INFO] 商户ID: '8034567958'
[2025-08-07 22:24:17] [INFO] 商品ID: ''
[2025-08-07 22:24:17] [INFO] 订单ID: ''
[2025-08-07 22:24:44] [INFO] === 新的访问开始 ===
[2025-08-07 22:24:44] [INFO] 请求URI: /shop.php?dd=4200002791202508071956668773
[2025-08-07 22:24:44] [INFO] 请求方法: GET
[2025-08-07 22:24:44] [INFO] GET参数: {"dd":"4200002791202508071956668773"}
[2025-08-07 22:24:44] [INFO] POST参数: []
[2025-08-07 22:24:44] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:24:44] [INFO] 客户端IP: **************
[2025-08-07 22:24:44] [INFO] 解析参数 - sj: '', sp: '', dd: '4200002791202508071956668773'
[2025-08-07 22:24:44] [INFO] 页面类型: order
[2025-08-07 22:24:44] [INFO] 商户ID: ''
[2025-08-07 22:24:44] [INFO] 商品ID: ''
[2025-08-07 22:24:44] [INFO] 订单ID: '4200002791202508071956668773'
[2025-08-07 22:27:03] [INFO] === 新的访问开始 ===
[2025-08-07 22:27:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:27:03] [INFO] 请求方法: GET
[2025-08-07 22:27:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:27:03] [INFO] POST参数: []
[2025-08-07 22:27:03] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:27:03] [INFO] 客户端IP: **************
[2025-08-07 22:27:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:27:03] [INFO] 页面类型: merchant
[2025-08-07 22:27:03] [INFO] 商户ID: '8034567958'
[2025-08-07 22:27:03] [INFO] 商品ID: ''
[2025-08-07 22:27:03] [INFO] 订单ID: ''
[2025-08-07 22:28:44] [INFO] === 新的访问开始 ===
[2025-08-07 22:28:44] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:28:44] [INFO] 请求方法: GET
[2025-08-07 22:28:44] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:28:44] [INFO] POST参数: []
[2025-08-07 22:28:44] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:28:44] [INFO] 客户端IP: **************
[2025-08-07 22:28:44] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:28:44] [INFO] 页面类型: merchant
[2025-08-07 22:28:44] [INFO] 商户ID: '8034567958'
[2025-08-07 22:28:44] [INFO] 商品ID: ''
[2025-08-07 22:28:44] [INFO] 订单ID: ''
[2025-08-07 22:28:57] [INFO] === 新的访问开始 ===
[2025-08-07 22:28:57] [INFO] 请求URI: /shop.php?dd=2025080722195934752
[2025-08-07 22:28:57] [INFO] 请求方法: GET
[2025-08-07 22:28:57] [INFO] GET参数: {"dd":"2025080722195934752"}
[2025-08-07 22:28:57] [INFO] POST参数: []
[2025-08-07 22:28:57] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:28:57] [INFO] 客户端IP: **************
[2025-08-07 22:28:57] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080722195934752'
[2025-08-07 22:28:57] [INFO] 页面类型: order
[2025-08-07 22:28:57] [INFO] 商户ID: ''
[2025-08-07 22:28:57] [INFO] 商品ID: ''
[2025-08-07 22:28:57] [INFO] 订单ID: '2025080722195934752'
[2025-08-07 22:29:09] [INFO] === 新的访问开始 ===
[2025-08-07 22:29:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:29:09] [INFO] 请求方法: GET
[2025-08-07 22:29:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:29:09] [INFO] POST参数: []
[2025-08-07 22:29:09] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 14; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.88 Mobile Safari/537.36
[2025-08-07 22:29:09] [INFO] 客户端IP: **************
[2025-08-07 22:29:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:29:09] [INFO] 页面类型: merchant
[2025-08-07 22:29:09] [INFO] 商户ID: '8034567958'
[2025-08-07 22:29:09] [INFO] 商品ID: ''
[2025-08-07 22:29:09] [INFO] 订单ID: ''
[2025-08-07 22:30:26] [INFO] === 新的访问开始 ===
[2025-08-07 22:30:26] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:30:26] [INFO] 请求方法: GET
[2025-08-07 22:30:26] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:30:26] [INFO] POST参数: []
[2025-08-07 22:30:26] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:30:26] [INFO] 客户端IP: **************
[2025-08-07 22:30:26] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:30:26] [INFO] 页面类型: merchant
[2025-08-07 22:30:26] [INFO] 商户ID: '8034567958'
[2025-08-07 22:30:26] [INFO] 商品ID: ''
[2025-08-07 22:30:26] [INFO] 订单ID: ''
[2025-08-07 22:32:50] [INFO] === 新的访问开始 ===
[2025-08-07 22:32:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:32:50] [INFO] 请求方法: GET
[2025-08-07 22:32:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:32:50] [INFO] POST参数: []
[2025-08-07 22:32:50] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:32:50] [INFO] 客户端IP: **************
[2025-08-07 22:32:50] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:32:50] [INFO] 页面类型: merchant
[2025-08-07 22:32:50] [INFO] 商户ID: '8034567958'
[2025-08-07 22:32:50] [INFO] 商品ID: ''
[2025-08-07 22:32:50] [INFO] 订单ID: ''
[2025-08-07 22:33:04] [INFO] === 新的访问开始 ===
[2025-08-07 22:33:04] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:33:04] [INFO] 请求方法: GET
[2025-08-07 22:33:04] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:33:04] [INFO] POST参数: []
[2025-08-07 22:33:04] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:33:04] [INFO] 客户端IP: **************
[2025-08-07 22:33:04] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:33:04] [INFO] 页面类型: merchant
[2025-08-07 22:33:04] [INFO] 商户ID: '8034567958'
[2025-08-07 22:33:04] [INFO] 商品ID: ''
[2025-08-07 22:33:04] [INFO] 订单ID: ''
[2025-08-07 22:34:51] [INFO] === 新的访问开始 ===
[2025-08-07 22:34:51] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:34:51] [INFO] 请求方法: GET
[2025-08-07 22:34:51] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:34:51] [INFO] POST参数: []
[2025-08-07 22:34:51] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:34:51] [INFO] 客户端IP: **************
[2025-08-07 22:34:51] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:34:51] [INFO] 页面类型: merchant
[2025-08-07 22:34:51] [INFO] 商户ID: '8034567958'
[2025-08-07 22:34:51] [INFO] 商品ID: ''
[2025-08-07 22:34:51] [INFO] 订单ID: ''
[2025-08-07 22:35:23] [INFO] === 新的访问开始 ===
[2025-08-07 22:35:23] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:35:23] [INFO] 请求方法: GET
[2025-08-07 22:35:23] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:35:23] [INFO] POST参数: []
[2025-08-07 22:35:23] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:35:23] [INFO] 客户端IP: **************
[2025-08-07 22:35:23] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:35:23] [INFO] 页面类型: merchant
[2025-08-07 22:35:23] [INFO] 商户ID: '8034567958'
[2025-08-07 22:35:23] [INFO] 商品ID: ''
[2025-08-07 22:35:23] [INFO] 订单ID: ''
[2025-08-07 22:35:33] [INFO] === 新的访问开始 ===
[2025-08-07 22:35:33] [INFO] 请求URI: /shop.php?dd=ORDER17545773028104
[2025-08-07 22:35:33] [INFO] 请求方法: GET
[2025-08-07 22:35:33] [INFO] GET参数: {"dd":"ORDER17545773028104"}
[2025-08-07 22:35:33] [INFO] POST参数: []
[2025-08-07 22:35:33] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:35:33] [INFO] 客户端IP: **************
[2025-08-07 22:35:33] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17545773028104'
[2025-08-07 22:35:33] [INFO] 页面类型: order
[2025-08-07 22:35:33] [INFO] 商户ID: ''
[2025-08-07 22:35:33] [INFO] 商品ID: ''
[2025-08-07 22:35:33] [INFO] 订单ID: 'ORDER17545773028104'
[2025-08-07 22:38:46] [INFO] === 新的访问开始 ===
[2025-08-07 22:38:46] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:38:46] [INFO] 请求方法: GET
[2025-08-07 22:38:46] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:38:46] [INFO] POST参数: []
[2025-08-07 22:38:46] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:38:46] [INFO] 客户端IP: **************
[2025-08-07 22:38:46] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:38:46] [INFO] 页面类型: merchant
[2025-08-07 22:38:46] [INFO] 商户ID: '8034567958'
[2025-08-07 22:38:46] [INFO] 商品ID: ''
[2025-08-07 22:38:46] [INFO] 订单ID: ''
[2025-08-07 22:39:41] [INFO] === 新的访问开始 ===
[2025-08-07 22:39:41] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:39:41] [INFO] 请求方法: GET
[2025-08-07 22:39:41] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:39:41] [INFO] POST参数: []
[2025-08-07 22:39:41] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:39:41] [INFO] 客户端IP: **************
[2025-08-07 22:39:41] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:39:41] [INFO] 页面类型: merchant
[2025-08-07 22:39:41] [INFO] 商户ID: '8034567958'
[2025-08-07 22:39:41] [INFO] 商品ID: ''
[2025-08-07 22:39:41] [INFO] 订单ID: ''
[2025-08-07 22:39:47] [INFO] === 新的访问开始 ===
[2025-08-07 22:39:47] [INFO] 请求URI: /shop.php?dd=75sc19it39wai8otlt
[2025-08-07 22:39:47] [INFO] 请求方法: GET
[2025-08-07 22:39:47] [INFO] GET参数: {"dd":"75sc19it39wai8otlt"}
[2025-08-07 22:39:47] [INFO] POST参数: []
[2025-08-07 22:39:47] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:39:47] [INFO] 客户端IP: **************
[2025-08-07 22:39:47] [INFO] 解析参数 - sj: '', sp: '', dd: '75sc19it39wai8otlt'
[2025-08-07 22:39:47] [INFO] 页面类型: order
[2025-08-07 22:39:47] [INFO] 商户ID: ''
[2025-08-07 22:39:47] [INFO] 商品ID: ''
[2025-08-07 22:39:47] [INFO] 订单ID: '75sc19it39wai8otlt'
[2025-08-07 22:40:03] [INFO] === 新的访问开始 ===
[2025-08-07 22:40:03] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:40:03] [INFO] 请求方法: GET
[2025-08-07 22:40:03] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:40:03] [INFO] POST参数: []
[2025-08-07 22:40:03] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:40:03] [INFO] 客户端IP: **************
[2025-08-07 22:40:03] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:40:03] [INFO] 页面类型: merchant
[2025-08-07 22:40:03] [INFO] 商户ID: '8034567958'
[2025-08-07 22:40:03] [INFO] 商品ID: ''
[2025-08-07 22:40:03] [INFO] 订单ID: ''
[2025-08-07 22:40:07] [INFO] === 新的访问开始 ===
[2025-08-07 22:40:07] [INFO] 请求URI: /shop.php?dd=web_1754577593547_uerirbfcx
[2025-08-07 22:40:07] [INFO] 请求方法: GET
[2025-08-07 22:40:07] [INFO] GET参数: {"dd":"web_1754577593547_uerirbfcx"}
[2025-08-07 22:40:07] [INFO] POST参数: []
[2025-08-07 22:40:07] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:40:07] [INFO] 客户端IP: **************
[2025-08-07 22:40:07] [INFO] 解析参数 - sj: '', sp: '', dd: 'web_1754577593547_uerirbfcx'
[2025-08-07 22:40:07] [INFO] 页面类型: order
[2025-08-07 22:40:07] [INFO] 商户ID: ''
[2025-08-07 22:40:07] [INFO] 商品ID: ''
[2025-08-07 22:40:07] [INFO] 订单ID: 'web_1754577593547_uerirbfcx'
[2025-08-07 22:43:20] [INFO] === 新的访问开始 ===
[2025-08-07 22:43:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:43:20] [INFO] 请求方法: GET
[2025-08-07 22:43:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:43:20] [INFO] POST参数: []
[2025-08-07 22:43:20] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:43:20] [INFO] 客户端IP: **************
[2025-08-07 22:43:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:43:20] [INFO] 页面类型: merchant
[2025-08-07 22:43:20] [INFO] 商户ID: '8034567958'
[2025-08-07 22:43:20] [INFO] 商品ID: ''
[2025-08-07 22:43:20] [INFO] 订单ID: ''
[2025-08-07 22:43:35] [INFO] === 新的访问开始 ===
[2025-08-07 22:43:35] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:43:35] [INFO] 请求方法: GET
[2025-08-07 22:43:35] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:43:35] [INFO] POST参数: []
[2025-08-07 22:43:35] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:43:35] [INFO] 客户端IP: **************
[2025-08-07 22:43:35] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:43:35] [INFO] 页面类型: merchant
[2025-08-07 22:43:35] [INFO] 商户ID: '8034567958'
[2025-08-07 22:43:35] [INFO] 商品ID: ''
[2025-08-07 22:43:35] [INFO] 订单ID: ''
[2025-08-07 22:44:12] [INFO] === 新的访问开始 ===
[2025-08-07 22:44:12] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:44:12] [INFO] 请求方法: GET
[2025-08-07 22:44:12] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:44:12] [INFO] POST参数: []
[2025-08-07 22:44:12] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:44:12] [INFO] 客户端IP: **************
[2025-08-07 22:44:12] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:44:12] [INFO] 页面类型: merchant
[2025-08-07 22:44:12] [INFO] 商户ID: '8034567958'
[2025-08-07 22:44:12] [INFO] 商品ID: ''
[2025-08-07 22:44:12] [INFO] 订单ID: ''
[2025-08-07 22:51:27] [INFO] === 新的访问开始 ===
[2025-08-07 22:51:27] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:51:27] [INFO] 请求方法: GET
[2025-08-07 22:51:27] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:51:27] [INFO] POST参数: []
[2025-08-07 22:51:27] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:51:27] [INFO] 客户端IP: **************
[2025-08-07 22:51:27] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:51:27] [INFO] 页面类型: merchant
[2025-08-07 22:51:27] [INFO] 商户ID: '8034567958'
[2025-08-07 22:51:27] [INFO] 商品ID: ''
[2025-08-07 22:51:27] [INFO] 订单ID: ''
[2025-08-07 22:52:09] [INFO] === 新的访问开始 ===
[2025-08-07 22:52:09] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:52:09] [INFO] 请求方法: GET
[2025-08-07 22:52:09] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:52:09] [INFO] POST参数: []
[2025-08-07 22:52:09] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.179 Mobile Safari/537.36
[2025-08-07 22:52:09] [INFO] 客户端IP: **************
[2025-08-07 22:52:09] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:52:09] [INFO] 页面类型: merchant
[2025-08-07 22:52:09] [INFO] 商户ID: '8034567958'
[2025-08-07 22:52:09] [INFO] 商品ID: ''
[2025-08-07 22:52:09] [INFO] 订单ID: ''
[2025-08-07 22:52:25] [INFO] === 新的访问开始 ===
[2025-08-07 22:52:25] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:52:25] [INFO] 请求方法: GET
[2025-08-07 22:52:25] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:52:25] [INFO] POST参数: []
[2025-08-07 22:52:25] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; PLA110 Build/UKQ1.231108.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/********
[2025-08-07 22:52:25] [INFO] 客户端IP: **************
[2025-08-07 22:52:25] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:52:25] [INFO] 页面类型: merchant
[2025-08-07 22:52:25] [INFO] 商户ID: '8034567958'
[2025-08-07 22:52:25] [INFO] 商品ID: ''
[2025-08-07 22:52:25] [INFO] 订单ID: ''
[2025-08-07 22:53:43] [INFO] === 新的访问开始 ===
[2025-08-07 22:53:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:53:43] [INFO] 请求方法: GET
[2025-08-07 22:53:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:53:43] [INFO] POST参数: []
[2025-08-07 22:53:43] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; PLA110 Build/UKQ1.231108.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/********
[2025-08-07 22:53:43] [INFO] 客户端IP: ************
[2025-08-07 22:53:43] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:53:43] [INFO] 页面类型: merchant
[2025-08-07 22:53:43] [INFO] 商户ID: '8034567958'
[2025-08-07 22:53:43] [INFO] 商品ID: ''
[2025-08-07 22:53:43] [INFO] 订单ID: ''
[2025-08-07 22:54:17] [INFO] === 新的访问开始 ===
[2025-08-07 22:54:17] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:54:17] [INFO] 请求方法: GET
[2025-08-07 22:54:17] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:54:17] [INFO] POST参数: []
[2025-08-07 22:54:17] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 6.0.1; SOV33 Build/35.0.D.0.326) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.91 Mobile Safari/537.36
[2025-08-07 22:54:17] [INFO] 客户端IP: ************
[2025-08-07 22:54:17] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:54:17] [INFO] 页面类型: merchant
[2025-08-07 22:54:17] [INFO] 商户ID: '8034567958'
[2025-08-07 22:54:17] [INFO] 商品ID: ''
[2025-08-07 22:54:17] [INFO] 订单ID: ''
[2025-08-07 22:55:24] [INFO] === 新的访问开始 ===
[2025-08-07 22:55:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 22:55:24] [INFO] 请求方法: GET
[2025-08-07 22:55:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 22:55:24] [INFO] POST参数: []
[2025-08-07 22:55:24] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; PLA110 Build/UKQ1.231108.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/********
[2025-08-07 22:55:24] [INFO] 客户端IP: ************
[2025-08-07 22:55:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 22:55:24] [INFO] 页面类型: merchant
[2025-08-07 22:55:24] [INFO] 商户ID: '8034567958'
[2025-08-07 22:55:24] [INFO] 商品ID: ''
[2025-08-07 22:55:24] [INFO] 订单ID: ''
[2025-08-07 22:55:28] [INFO] === 新的访问开始 ===
[2025-08-07 22:55:28] [INFO] 请求URI: /shop.php?dd=ORDER17545784345800
[2025-08-07 22:55:28] [INFO] 请求方法: GET
[2025-08-07 22:55:28] [INFO] GET参数: {"dd":"ORDER17545784345800"}
[2025-08-07 22:55:28] [INFO] POST参数: []
[2025-08-07 22:55:28] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; PLA110 Build/UKQ1.231108.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/********
[2025-08-07 22:55:28] [INFO] 客户端IP: ************
[2025-08-07 22:55:28] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17545784345800'
[2025-08-07 22:55:28] [INFO] 页面类型: order
[2025-08-07 22:55:28] [INFO] 商户ID: ''
[2025-08-07 22:55:28] [INFO] 商品ID: ''
[2025-08-07 22:55:28] [INFO] 订单ID: 'ORDER17545784345800'
[2025-08-07 22:56:23] [INFO] === 新的访问开始 ===
[2025-08-07 22:56:23] [INFO] 请求URI: /shop.php?dd=ORDER17545784345800
[2025-08-07 22:56:23] [INFO] 请求方法: GET
[2025-08-07 22:56:23] [INFO] GET参数: {"dd":"ORDER17545784345800"}
[2025-08-07 22:56:23] [INFO] POST参数: []
[2025-08-07 22:56:23] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 6.0.1; SOV33 Build/35.0.D.0.326) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.91 Mobile Safari/537.36
[2025-08-07 22:56:23] [INFO] 客户端IP: ************
[2025-08-07 22:56:23] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17545784345800'
[2025-08-07 22:56:23] [INFO] 页面类型: order
[2025-08-07 22:56:23] [INFO] 商户ID: ''
[2025-08-07 22:56:23] [INFO] 商品ID: ''
[2025-08-07 22:56:23] [INFO] 订单ID: 'ORDER17545784345800'
[2025-08-07 23:42:45] [INFO] === 新的访问开始 ===
[2025-08-07 23:42:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 23:42:45] [INFO] 请求方法: GET
[2025-08-07 23:42:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 23:42:45] [INFO] POST参数: []
[2025-08-07 23:42:45] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-07 23:42:45] [INFO] 客户端IP: *************
[2025-08-07 23:42:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 23:42:45] [INFO] 页面类型: merchant
[2025-08-07 23:42:45] [INFO] 商户ID: '8034567958'
[2025-08-07 23:42:45] [INFO] 商品ID: ''
[2025-08-07 23:42:45] [INFO] 订单ID: ''
[2025-08-07 23:43:52] [INFO] === 新的访问开始 ===
[2025-08-07 23:43:52] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 23:43:52] [INFO] 请求方法: GET
[2025-08-07 23:43:52] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 23:43:52] [INFO] POST参数: []
[2025-08-07 23:43:52] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-07 23:43:52] [INFO] 客户端IP: *************
[2025-08-07 23:43:52] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 23:43:52] [INFO] 页面类型: merchant
[2025-08-07 23:43:52] [INFO] 商户ID: '8034567958'
[2025-08-07 23:43:52] [INFO] 商品ID: ''
[2025-08-07 23:43:52] [INFO] 订单ID: ''
[2025-08-07 23:44:27] [INFO] === 新的访问开始 ===
[2025-08-07 23:44:27] [INFO] 请求URI: /shop.php?dd=83620250807611927142
[2025-08-07 23:44:27] [INFO] 请求方法: GET
[2025-08-07 23:44:27] [INFO] GET参数: {"dd":"83620250807611927142"}
[2025-08-07 23:44:27] [INFO] POST参数: []
[2025-08-07 23:44:27] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-07 23:44:27] [INFO] 客户端IP: *************
[2025-08-07 23:44:27] [INFO] 解析参数 - sj: '', sp: '', dd: '83620250807611927142'
[2025-08-07 23:44:27] [INFO] 页面类型: order
[2025-08-07 23:44:27] [INFO] 商户ID: ''
[2025-08-07 23:44:27] [INFO] 商品ID: ''
[2025-08-07 23:44:27] [INFO] 订单ID: '83620250807611927142'
[2025-08-07 23:44:46] [INFO] === 新的访问开始 ===
[2025-08-07 23:44:46] [INFO] 请求URI: /shop.php?dd=ORDER17545813875152
[2025-08-07 23:44:46] [INFO] 请求方法: GET
[2025-08-07 23:44:46] [INFO] GET参数: {"dd":"ORDER17545813875152"}
[2025-08-07 23:44:46] [INFO] POST参数: []
[2025-08-07 23:44:46] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-07 23:44:46] [INFO] 客户端IP: *************
[2025-08-07 23:44:46] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17545813875152'
[2025-08-07 23:44:46] [INFO] 页面类型: order
[2025-08-07 23:44:46] [INFO] 商户ID: ''
[2025-08-07 23:44:46] [INFO] 商品ID: ''
[2025-08-07 23:44:46] [INFO] 订单ID: 'ORDER17545813875152'
[2025-08-07 23:46:06] [INFO] === 新的访问开始 ===
[2025-08-07 23:46:06] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-07 23:46:06] [INFO] 请求方法: GET
[2025-08-07 23:46:06] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-07 23:46:06] [INFO] POST参数: []
[2025-08-07 23:46:06] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.168 Mobile Safari/537.36
[2025-08-07 23:46:06] [INFO] 客户端IP: *************
[2025-08-07 23:46:06] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-07 23:46:06] [INFO] 页面类型: merchant
[2025-08-07 23:46:06] [INFO] 商户ID: '8034567958'
[2025-08-07 23:46:06] [INFO] 商品ID: ''
[2025-08-07 23:46:06] [INFO] 订单ID: ''
[2025-08-08 00:36:50] [INFO] === 新的访问开始 ===
[2025-08-08 00:36:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 00:36:50] [INFO] 请求方法: GET
[2025-08-08 00:36:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 00:36:50] [INFO] POST参数: []
[2025-08-08 00:36:50] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 00:36:50] [INFO] 客户端IP: **************
[2025-08-08 00:36:50] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 00:36:50] [INFO] 页面类型: merchant
[2025-08-08 00:36:50] [INFO] 商户ID: '8034567958'
[2025-08-08 00:36:50] [INFO] 商品ID: ''
[2025-08-08 00:36:50] [INFO] 订单ID: ''
[2025-08-08 00:57:22] [INFO] === 新的访问开始 ===
[2025-08-08 00:57:22] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 00:57:22] [INFO] 请求方法: GET
[2025-08-08 00:57:22] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 00:57:22] [INFO] POST参数: []
[2025-08-08 00:57:22] [INFO] User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.54 Safari/537.36
[2025-08-08 00:57:22] [INFO] 客户端IP: **************
[2025-08-08 00:57:22] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 00:57:22] [INFO] 页面类型: merchant
[2025-08-08 00:57:22] [INFO] 商户ID: '8034567958'
[2025-08-08 00:57:22] [INFO] 商品ID: ''
[2025-08-08 00:57:22] [INFO] 订单ID: ''
[2025-08-08 02:13:42] [INFO] === 新的访问开始 ===
[2025-08-08 02:13:42] [INFO] 请求URI: /shop.php?sp=23
[2025-08-08 02:13:42] [INFO] 请求方法: GET
[2025-08-08 02:13:42] [INFO] GET参数: {"sp":"23"}
[2025-08-08 02:13:42] [INFO] POST参数: []
[2025-08-08 02:13:42] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-08 02:13:42] [INFO] 客户端IP: **************
[2025-08-08 02:13:42] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-08 02:13:42] [INFO] 页面类型: product
[2025-08-08 02:13:42] [INFO] 商户ID: ''
[2025-08-08 02:13:42] [INFO] 商品ID: '23'
[2025-08-08 02:13:42] [INFO] 订单ID: ''
[2025-08-08 02:15:29] [INFO] === 新的访问开始 ===
[2025-08-08 02:15:29] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 02:15:29] [INFO] 请求方法: GET
[2025-08-08 02:15:29] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 02:15:29] [INFO] POST参数: []
[2025-08-08 02:15:29] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-08 02:15:29] [INFO] 客户端IP: **************
[2025-08-08 02:15:29] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 02:15:29] [INFO] 页面类型: merchant
[2025-08-08 02:15:29] [INFO] 商户ID: '8034567958'
[2025-08-08 02:15:29] [INFO] 商品ID: ''
[2025-08-08 02:15:29] [INFO] 订单ID: ''
[2025-08-08 02:18:34] [INFO] === 新的访问开始 ===
[2025-08-08 02:18:34] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 02:18:34] [INFO] 请求方法: GET
[2025-08-08 02:18:34] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 02:18:34] [INFO] POST参数: []
[2025-08-08 02:18:34] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-08 02:18:34] [INFO] 客户端IP: **************
[2025-08-08 02:18:34] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 02:18:34] [INFO] 页面类型: merchant
[2025-08-08 02:18:34] [INFO] 商户ID: '8034567958'
[2025-08-08 02:18:34] [INFO] 商品ID: ''
[2025-08-08 02:18:34] [INFO] 订单ID: ''
[2025-08-08 02:22:53] [INFO] === 新的访问开始 ===
[2025-08-08 02:22:53] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 02:22:53] [INFO] 请求方法: GET
[2025-08-08 02:22:53] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 02:22:53] [INFO] POST参数: []
[2025-08-08 02:22:53] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-08 02:22:53] [INFO] 客户端IP: **************
[2025-08-08 02:22:53] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 02:22:53] [INFO] 页面类型: merchant
[2025-08-08 02:22:53] [INFO] 商户ID: '8034567958'
[2025-08-08 02:22:53] [INFO] 商品ID: ''
[2025-08-08 02:22:53] [INFO] 订单ID: ''
[2025-08-08 02:23:08] [INFO] === 新的访问开始 ===
[2025-08-08 02:23:08] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 02:23:08] [INFO] 请求方法: GET
[2025-08-08 02:23:08] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 02:23:08] [INFO] POST参数: []
[2025-08-08 02:23:08] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; V2403A; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/131.0.6778.200 Mobile Safari/537.36 VivoBrowser/********
[2025-08-08 02:23:08] [INFO] 客户端IP: **************
[2025-08-08 02:23:08] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 02:23:08] [INFO] 页面类型: merchant
[2025-08-08 02:23:08] [INFO] 商户ID: '8034567958'
[2025-08-08 02:23:08] [INFO] 商品ID: ''
[2025-08-08 02:23:08] [INFO] 订单ID: ''
[2025-08-08 02:32:04] [INFO] === 新的访问开始 ===
[2025-08-08 02:32:04] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 02:32:04] [INFO] 请求方法: GET
[2025-08-08 02:32:04] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 02:32:04] [INFO] POST参数: []
[2025-08-08 02:32:04] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-08 02:32:04] [INFO] 客户端IP: ************
[2025-08-08 02:32:04] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 02:32:04] [INFO] 页面类型: merchant
[2025-08-08 02:32:04] [INFO] 商户ID: '8034567958'
[2025-08-08 02:32:04] [INFO] 商品ID: ''
[2025-08-08 02:32:04] [INFO] 订单ID: ''
[2025-08-08 02:34:01] [INFO] === 新的访问开始 ===
[2025-08-08 02:34:01] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 02:34:01] [INFO] 请求方法: GET
[2025-08-08 02:34:01] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 02:34:01] [INFO] POST参数: []
[2025-08-08 02:34:01] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
[2025-08-08 02:34:01] [INFO] 客户端IP: ************
[2025-08-08 02:34:01] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 02:34:01] [INFO] 页面类型: merchant
[2025-08-08 02:34:01] [INFO] 商户ID: '8034567958'
[2025-08-08 02:34:01] [INFO] 商品ID: ''
[2025-08-08 02:34:01] [INFO] 订单ID: ''
[2025-08-08 06:36:38] [INFO] === 新的访问开始 ===
[2025-08-08 06:36:38] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 06:36:38] [INFO] 请求方法: GET
[2025-08-08 06:36:38] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 06:36:38] [INFO] POST参数: []
[2025-08-08 06:36:38] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.60 Mobile Safari/537.36
[2025-08-08 06:36:38] [INFO] 客户端IP: ************
[2025-08-08 06:36:38] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 06:36:38] [INFO] 页面类型: merchant
[2025-08-08 06:36:38] [INFO] 商户ID: '8034567958'
[2025-08-08 06:36:38] [INFO] 商品ID: ''
[2025-08-08 06:36:38] [INFO] 订单ID: ''
[2025-08-08 06:37:05] [INFO] === 新的访问开始 ===
[2025-08-08 06:37:05] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 06:37:05] [INFO] 请求方法: GET
[2025-08-08 06:37:05] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 06:37:05] [INFO] POST参数: []
[2025-08-08 06:37:05] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.60 Mobile Safari/537.36
[2025-08-08 06:37:05] [INFO] 客户端IP: ************
[2025-08-08 06:37:05] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 06:37:05] [INFO] 页面类型: merchant
[2025-08-08 06:37:05] [INFO] 商户ID: '8034567958'
[2025-08-08 06:37:05] [INFO] 商品ID: ''
[2025-08-08 06:37:05] [INFO] 订单ID: ''
[2025-08-08 06:37:16] [INFO] === 新的访问开始 ===
[2025-08-08 06:37:16] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 06:37:16] [INFO] 请求方法: GET
[2025-08-08 06:37:16] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 06:37:16] [INFO] POST参数: []
[2025-08-08 06:37:16] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.60 Mobile Safari/537.36
[2025-08-08 06:37:16] [INFO] 客户端IP: ************
[2025-08-08 06:37:16] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 06:37:16] [INFO] 页面类型: merchant
[2025-08-08 06:37:16] [INFO] 商户ID: '8034567958'
[2025-08-08 06:37:16] [INFO] 商品ID: ''
[2025-08-08 06:37:16] [INFO] 订单ID: ''
[2025-08-08 06:37:33] [INFO] === 新的访问开始 ===
[2025-08-08 06:37:33] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 06:37:33] [INFO] 请求方法: GET
[2025-08-08 06:37:33] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 06:37:33] [INFO] POST参数: []
[2025-08-08 06:37:33] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.60 Mobile Safari/537.36
[2025-08-08 06:37:33] [INFO] 客户端IP: ************
[2025-08-08 06:37:33] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 06:37:33] [INFO] 页面类型: merchant
[2025-08-08 06:37:33] [INFO] 商户ID: '8034567958'
[2025-08-08 06:37:33] [INFO] 商品ID: ''
[2025-08-08 06:37:33] [INFO] 订单ID: ''
[2025-08-08 06:43:16] [INFO] === 新的访问开始 ===
[2025-08-08 06:43:16] [INFO] 请求URI: /shop.php?sp=23
[2025-08-08 06:43:16] [INFO] 请求方法: GET
[2025-08-08 06:43:16] [INFO] GET参数: {"sp":"23"}
[2025-08-08 06:43:16] [INFO] POST参数: []
[2025-08-08 06:43:16] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 15; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.60 Mobile Safari/537.36
[2025-08-08 06:43:16] [INFO] 客户端IP: ************
[2025-08-08 06:43:16] [INFO] 解析参数 - sj: '', sp: '23', dd: ''
[2025-08-08 06:43:16] [INFO] 页面类型: product
[2025-08-08 06:43:16] [INFO] 商户ID: ''
[2025-08-08 06:43:16] [INFO] 商品ID: '23'
[2025-08-08 06:43:16] [INFO] 订单ID: ''
[2025-08-08 07:21:58] [INFO] === 新的访问开始 ===
[2025-08-08 07:21:58] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 07:21:58] [INFO] 请求方法: GET
[2025-08-08 07:21:58] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 07:21:58] [INFO] POST参数: []
[2025-08-08 07:21:58] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1
[2025-08-08 07:21:58] [INFO] 客户端IP: ************
[2025-08-08 07:21:58] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 07:21:58] [INFO] 页面类型: merchant
[2025-08-08 07:21:58] [INFO] 商户ID: '8034567958'
[2025-08-08 07:21:58] [INFO] 商品ID: ''
[2025-08-08 07:21:58] [INFO] 订单ID: ''
[2025-08-08 08:10:45] [INFO] === 新的访问开始 ===
[2025-08-08 08:10:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 08:10:45] [INFO] 请求方法: GET
[2025-08-08 08:10:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 08:10:45] [INFO] POST参数: []
[2025-08-08 08:10:45] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-08 08:10:45] [INFO] 客户端IP: *************
[2025-08-08 08:10:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 08:10:45] [INFO] 页面类型: merchant
[2025-08-08 08:10:45] [INFO] 商户ID: '8034567958'
[2025-08-08 08:10:45] [INFO] 商品ID: ''
[2025-08-08 08:10:45] [INFO] 订单ID: ''
[2025-08-08 08:11:22] [INFO] === 新的访问开始 ===
[2025-08-08 08:11:22] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 08:11:22] [INFO] 请求方法: GET
[2025-08-08 08:11:22] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 08:11:22] [INFO] POST参数: []
[2025-08-08 08:11:22] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-08 08:11:22] [INFO] 客户端IP: *************
[2025-08-08 08:11:22] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 08:11:22] [INFO] 页面类型: merchant
[2025-08-08 08:11:22] [INFO] 商户ID: '8034567958'
[2025-08-08 08:11:22] [INFO] 商品ID: ''
[2025-08-08 08:11:22] [INFO] 订单ID: ''
[2025-08-08 08:13:15] [INFO] === 新的访问开始 ===
[2025-08-08 08:13:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 08:13:15] [INFO] 请求方法: GET
[2025-08-08 08:13:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 08:13:15] [INFO] POST参数: []
[2025-08-08 08:13:15] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-08 08:13:15] [INFO] 客户端IP: **************
[2025-08-08 08:13:15] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 08:13:15] [INFO] 页面类型: merchant
[2025-08-08 08:13:15] [INFO] 商户ID: '8034567958'
[2025-08-08 08:13:15] [INFO] 商品ID: ''
[2025-08-08 08:13:15] [INFO] 订单ID: ''
[2025-08-08 08:14:01] [INFO] === 新的访问开始 ===
[2025-08-08 08:14:01] [INFO] 请求URI: /shop.php?dd=ORDER17546118913827
[2025-08-08 08:14:01] [INFO] 请求方法: GET
[2025-08-08 08:14:01] [INFO] GET参数: {"dd":"ORDER17546118913827"}
[2025-08-08 08:14:01] [INFO] POST参数: []
[2025-08-08 08:14:01] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-08 08:14:01] [INFO] 客户端IP: **************
[2025-08-08 08:14:01] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17546118913827'
[2025-08-08 08:14:01] [INFO] 页面类型: order
[2025-08-08 08:14:01] [INFO] 商户ID: ''
[2025-08-08 08:14:01] [INFO] 商品ID: ''
[2025-08-08 08:14:01] [INFO] 订单ID: 'ORDER17546118913827'
[2025-08-08 08:14:23] [INFO] === 新的访问开始 ===
[2025-08-08 08:14:23] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 08:14:23] [INFO] 请求方法: GET
[2025-08-08 08:14:23] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 08:14:23] [INFO] POST参数: []
[2025-08-08 08:14:23] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-08 08:14:23] [INFO] 客户端IP: **************
[2025-08-08 08:14:23] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 08:14:23] [INFO] 页面类型: merchant
[2025-08-08 08:14:23] [INFO] 商户ID: '8034567958'
[2025-08-08 08:14:23] [INFO] 商品ID: ''
[2025-08-08 08:14:23] [INFO] 订单ID: ''
[2025-08-08 08:14:28] [INFO] === 新的访问开始 ===
[2025-08-08 08:14:28] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 08:14:28] [INFO] 请求方法: GET
[2025-08-08 08:14:28] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 08:14:28] [INFO] POST参数: []
[2025-08-08 08:14:28] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-08 08:14:28] [INFO] 客户端IP: **************
[2025-08-08 08:14:28] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 08:14:28] [INFO] 页面类型: merchant
[2025-08-08 08:14:28] [INFO] 商户ID: '8034567958'
[2025-08-08 08:14:28] [INFO] 商品ID: ''
[2025-08-08 08:14:28] [INFO] 订单ID: ''
[2025-08-08 08:15:24] [INFO] === 新的访问开始 ===
[2025-08-08 08:15:24] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 08:15:24] [INFO] 请求方法: GET
[2025-08-08 08:15:24] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 08:15:24] [INFO] POST参数: []
[2025-08-08 08:15:24] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; PLA110 Build/UKQ1.231108.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/********
[2025-08-08 08:15:24] [INFO] 客户端IP: ************
[2025-08-08 08:15:24] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 08:15:24] [INFO] 页面类型: merchant
[2025-08-08 08:15:24] [INFO] 商户ID: '8034567958'
[2025-08-08 08:15:24] [INFO] 商品ID: ''
[2025-08-08 08:15:24] [INFO] 订单ID: ''
[2025-08-08 08:15:45] [INFO] === 新的访问开始 ===
[2025-08-08 08:15:45] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 08:15:45] [INFO] 请求方法: GET
[2025-08-08 08:15:45] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 08:15:45] [INFO] POST参数: []
[2025-08-08 08:15:45] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Safari/537.36
[2025-08-08 08:15:45] [INFO] 客户端IP: **************
[2025-08-08 08:15:45] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 08:15:45] [INFO] 页面类型: merchant
[2025-08-08 08:15:45] [INFO] 商户ID: '8034567958'
[2025-08-08 08:15:45] [INFO] 商品ID: ''
[2025-08-08 08:15:45] [INFO] 订单ID: ''
[2025-08-08 10:03:23] [INFO] === 新的访问开始 ===
[2025-08-08 10:03:23] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:03:23] [INFO] 请求方法: GET
[2025-08-08 10:03:23] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:03:23] [INFO] POST参数: []
[2025-08-08 10:03:23] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-08 10:03:23] [INFO] 客户端IP: **************
[2025-08-08 10:03:23] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:03:23] [INFO] 页面类型: merchant
[2025-08-08 10:03:23] [INFO] 商户ID: '8034567958'
[2025-08-08 10:03:23] [INFO] 商品ID: ''
[2025-08-08 10:03:23] [INFO] 订单ID: ''
[2025-08-08 10:03:26] [INFO] === 新的访问开始 ===
[2025-08-08 10:03:26] [INFO] 请求URI: /shop.php?dd=ORDER17545763979605
[2025-08-08 10:03:26] [INFO] 请求方法: GET
[2025-08-08 10:03:26] [INFO] GET参数: {"dd":"ORDER17545763979605"}
[2025-08-08 10:03:26] [INFO] POST参数: []
[2025-08-08 10:03:26] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-08 10:03:26] [INFO] 客户端IP: **************
[2025-08-08 10:03:26] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17545763979605'
[2025-08-08 10:03:26] [INFO] 页面类型: order
[2025-08-08 10:03:26] [INFO] 商户ID: ''
[2025-08-08 10:03:26] [INFO] 商品ID: ''
[2025-08-08 10:03:26] [INFO] 订单ID: 'ORDER17545763979605'
[2025-08-08 10:34:39] [INFO] === 新的访问开始 ===
[2025-08-08 10:34:39] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:34:39] [INFO] 请求方法: GET
[2025-08-08 10:34:39] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:34:39] [INFO] POST参数: []
[2025-08-08 10:34:39] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; PLA110 Build/UKQ1.231108.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/********
[2025-08-08 10:34:39] [INFO] 客户端IP: ************
[2025-08-08 10:34:39] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:34:39] [INFO] 页面类型: merchant
[2025-08-08 10:34:39] [INFO] 商户ID: '8034567958'
[2025-08-08 10:34:39] [INFO] 商品ID: ''
[2025-08-08 10:34:39] [INFO] 订单ID: ''
[2025-08-08 10:35:08] [INFO] === 新的访问开始 ===
[2025-08-08 10:35:08] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:35:08] [INFO] 请求方法: GET
[2025-08-08 10:35:08] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:35:08] [INFO] POST参数: []
[2025-08-08 10:35:08] [INFO] User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-cn; PLA110 Build/UKQ1.231108.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/115.0.5790.168 Mobile Safari/537.36 HeyTapBrowser/********
[2025-08-08 10:35:08] [INFO] 客户端IP: ************
[2025-08-08 10:35:08] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:35:08] [INFO] 页面类型: merchant
[2025-08-08 10:35:08] [INFO] 商户ID: '8034567958'
[2025-08-08 10:35:08] [INFO] 商品ID: ''
[2025-08-08 10:35:08] [INFO] 订单ID: ''
[2025-08-08 10:39:50] [INFO] === 新的访问开始 ===
[2025-08-08 10:39:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:39:50] [INFO] 请求方法: GET
[2025-08-08 10:39:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:39:50] [INFO] POST参数: []
[2025-08-08 10:39:50] [INFO] User-Agent: Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.101 Mobile Safari/537.36
[2025-08-08 10:39:50] [INFO] 客户端IP: **************
[2025-08-08 10:39:50] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:39:50] [INFO] 页面类型: merchant
[2025-08-08 10:39:50] [INFO] 商户ID: '8034567958'
[2025-08-08 10:39:50] [INFO] 商品ID: ''
[2025-08-08 10:39:50] [INFO] 订单ID: ''
[2025-08-08 10:50:23] [INFO] === 新的访问开始 ===
[2025-08-08 10:50:23] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:50:23] [INFO] 请求方法: GET
[2025-08-08 10:50:23] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:50:23] [INFO] POST参数: []
[2025-08-08 10:50:23] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:50:23] [INFO] 客户端IP: **************
[2025-08-08 10:50:23] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:50:23] [INFO] 页面类型: merchant
[2025-08-08 10:50:23] [INFO] 商户ID: '8034567958'
[2025-08-08 10:50:23] [INFO] 商品ID: ''
[2025-08-08 10:50:23] [INFO] 订单ID: ''
[2025-08-08 10:51:20] [INFO] === 新的访问开始 ===
[2025-08-08 10:51:20] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:51:20] [INFO] 请求方法: GET
[2025-08-08 10:51:20] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:51:20] [INFO] POST参数: []
[2025-08-08 10:51:20] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:51:20] [INFO] 客户端IP: **************
[2025-08-08 10:51:20] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:51:20] [INFO] 页面类型: merchant
[2025-08-08 10:51:20] [INFO] 商户ID: '8034567958'
[2025-08-08 10:51:20] [INFO] 商品ID: ''
[2025-08-08 10:51:20] [INFO] 订单ID: ''
[2025-08-08 10:53:30] [INFO] === 新的访问开始 ===
[2025-08-08 10:53:30] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:53:30] [INFO] 请求方法: GET
[2025-08-08 10:53:30] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:53:30] [INFO] POST参数: []
[2025-08-08 10:53:30] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:53:30] [INFO] 客户端IP: **************
[2025-08-08 10:53:30] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:53:30] [INFO] 页面类型: merchant
[2025-08-08 10:53:30] [INFO] 商户ID: '8034567958'
[2025-08-08 10:53:30] [INFO] 商品ID: ''
[2025-08-08 10:53:30] [INFO] 订单ID: ''
[2025-08-08 10:54:26] [INFO] === 新的访问开始 ===
[2025-08-08 10:54:26] [INFO] 请求URI: /shop.php?dd=%EF%BD%9C2025080810513168514
[2025-08-08 10:54:26] [INFO] 请求方法: GET
[2025-08-08 10:54:26] [INFO] GET参数: {"dd":"\uff5c2025080810513168514"}
[2025-08-08 10:54:26] [INFO] POST参数: []
[2025-08-08 10:54:26] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:54:26] [INFO] 客户端IP: **************
[2025-08-08 10:54:26] [INFO] 解析参数 - sj: '', sp: '', dd: '｜2025080810513168514'
[2025-08-08 10:54:26] [INFO] 页面类型: order
[2025-08-08 10:54:26] [INFO] 商户ID: ''
[2025-08-08 10:54:26] [INFO] 商品ID: ''
[2025-08-08 10:54:26] [INFO] 订单ID: '｜2025080810513168514'
[2025-08-08 10:54:37] [INFO] === 新的访问开始 ===
[2025-08-08 10:54:37] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:54:37] [INFO] 请求方法: GET
[2025-08-08 10:54:37] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:54:37] [INFO] POST参数: []
[2025-08-08 10:54:37] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:54:37] [INFO] 客户端IP: **************
[2025-08-08 10:54:37] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:54:37] [INFO] 页面类型: merchant
[2025-08-08 10:54:37] [INFO] 商户ID: '8034567958'
[2025-08-08 10:54:37] [INFO] 商品ID: ''
[2025-08-08 10:54:37] [INFO] 订单ID: ''
[2025-08-08 10:54:45] [INFO] === 新的访问开始 ===
[2025-08-08 10:54:45] [INFO] 请求URI: /shop.php?dd=2025080810513168514
[2025-08-08 10:54:45] [INFO] 请求方法: GET
[2025-08-08 10:54:45] [INFO] GET参数: {"dd":"2025080810513168514"}
[2025-08-08 10:54:45] [INFO] POST参数: []
[2025-08-08 10:54:45] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:54:45] [INFO] 客户端IP: **************
[2025-08-08 10:54:45] [INFO] 解析参数 - sj: '', sp: '', dd: '2025080810513168514'
[2025-08-08 10:54:45] [INFO] 页面类型: order
[2025-08-08 10:54:45] [INFO] 商户ID: ''
[2025-08-08 10:54:45] [INFO] 商品ID: ''
[2025-08-08 10:54:45] [INFO] 订单ID: '2025080810513168514'
[2025-08-08 10:56:04] [INFO] === 新的访问开始 ===
[2025-08-08 10:56:04] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:56:04] [INFO] 请求方法: GET
[2025-08-08 10:56:04] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:56:04] [INFO] POST参数: []
[2025-08-08 10:56:04] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:56:04] [INFO] 客户端IP: ************
[2025-08-08 10:56:04] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:56:04] [INFO] 页面类型: merchant
[2025-08-08 10:56:04] [INFO] 商户ID: '8034567958'
[2025-08-08 10:56:04] [INFO] 商品ID: ''
[2025-08-08 10:56:04] [INFO] 订单ID: ''
[2025-08-08 10:56:12] [INFO] === 新的访问开始 ===
[2025-08-08 10:56:12] [INFO] 请求URI: /shop.php?dd=4200002775202508081069872106
[2025-08-08 10:56:12] [INFO] 请求方法: GET
[2025-08-08 10:56:12] [INFO] GET参数: {"dd":"4200002775202508081069872106"}
[2025-08-08 10:56:12] [INFO] POST参数: []
[2025-08-08 10:56:12] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/22F76 Safari/604.1
[2025-08-08 10:56:12] [INFO] 客户端IP: ************
[2025-08-08 10:56:12] [INFO] 解析参数 - sj: '', sp: '', dd: '4200002775202508081069872106'
[2025-08-08 10:56:12] [INFO] 页面类型: order
[2025-08-08 10:56:12] [INFO] 商户ID: ''
[2025-08-08 10:56:12] [INFO] 商品ID: ''
[2025-08-08 10:56:12] [INFO] 订单ID: '4200002775202508081069872106'
[2025-08-08 10:57:10] [INFO] === 新的访问开始 ===
[2025-08-08 10:57:10] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 10:57:10] [INFO] 请求方法: GET
[2025-08-08 10:57:10] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 10:57:10] [INFO] POST参数: []
[2025-08-08 10:57:10] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-08 10:57:10] [INFO] 客户端IP: **************
[2025-08-08 10:57:10] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 10:57:10] [INFO] 页面类型: merchant
[2025-08-08 10:57:10] [INFO] 商户ID: '8034567958'
[2025-08-08 10:57:10] [INFO] 商品ID: ''
[2025-08-08 10:57:10] [INFO] 订单ID: ''
[2025-08-08 10:57:40] [INFO] === 新的访问开始 ===
[2025-08-08 10:57:40] [INFO] 请求URI: /shop.php?dd=4200002775202508081069872106
[2025-08-08 10:57:40] [INFO] 请求方法: GET
[2025-08-08 10:57:40] [INFO] GET参数: {"dd":"4200002775202508081069872106"}
[2025-08-08 10:57:40] [INFO] POST参数: []
[2025-08-08 10:57:40] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-08 10:57:40] [INFO] 客户端IP: **************
[2025-08-08 10:57:40] [INFO] 解析参数 - sj: '', sp: '', dd: '4200002775202508081069872106'
[2025-08-08 10:57:40] [INFO] 页面类型: order
[2025-08-08 10:57:40] [INFO] 商户ID: ''
[2025-08-08 10:57:40] [INFO] 商品ID: ''
[2025-08-08 10:57:40] [INFO] 订单ID: '4200002775202508081069872106'
[2025-08-08 10:57:58] [INFO] === 新的访问开始 ===
[2025-08-08 10:57:58] [INFO] 请求URI: /shop.php?dd=202508081051411312000144
[2025-08-08 10:57:58] [INFO] 请求方法: GET
[2025-08-08 10:57:58] [INFO] GET参数: {"dd":"202508081051411312000144"}
[2025-08-08 10:57:58] [INFO] POST参数: []
[2025-08-08 10:57:58] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-08 10:57:58] [INFO] 客户端IP: **************
[2025-08-08 10:57:58] [INFO] 解析参数 - sj: '', sp: '', dd: '202508081051411312000144'
[2025-08-08 10:57:58] [INFO] 页面类型: order
[2025-08-08 10:57:58] [INFO] 商户ID: ''
[2025-08-08 10:57:58] [INFO] 商品ID: ''
[2025-08-08 10:57:58] [INFO] 订单ID: '202508081051411312000144'
[2025-08-08 11:00:12] [INFO] === 新的访问开始 ===
[2025-08-08 11:00:12] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-08 11:00:12] [INFO] 请求方法: GET
[2025-08-08 11:00:12] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-08 11:00:12] [INFO] POST参数: []
[2025-08-08 11:00:12] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-08 11:00:12] [INFO] 客户端IP: **************
[2025-08-08 11:00:12] [INFO] 解析参数 - sj: '8034567958', sp: '', dd: ''
[2025-08-08 11:00:12] [INFO] 页面类型: merchant
[2025-08-08 11:00:12] [INFO] 商户ID: '8034567958'
[2025-08-08 11:00:12] [INFO] 商品ID: ''
[2025-08-08 11:00:12] [INFO] 订单ID: ''
[2025-08-08 11:01:53] [INFO] === 新的访问开始 ===
[2025-08-08 11:01:53] [INFO] 请求URI: /shop.php?dd=ORDER17546214891180
[2025-08-08 11:01:53] [INFO] 请求方法: GET
[2025-08-08 11:01:53] [INFO] GET参数: {"dd":"ORDER17546214891180"}
[2025-08-08 11:01:53] [INFO] POST参数: []
[2025-08-08 11:01:53] [INFO] User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Mobile/15E148 Safari/604.1
[2025-08-08 11:01:53] [INFO] 客户端IP: **************
[2025-08-08 11:01:53] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17546214891180'
[2025-08-08 11:01:53] [INFO] 页面类型: order
[2025-08-08 11:01:53] [INFO] 商户ID: ''
[2025-08-08 11:01:53] [INFO] 商品ID: ''
[2025-08-08 11:01:53] [INFO] 订单ID: 'ORDER17546214891180'
