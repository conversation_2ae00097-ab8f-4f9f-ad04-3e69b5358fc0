# Nginx配置规则
# 如果您使用的是小皮面板，请将以下配置添加到站点的nginx配置中

# 处理PHP文件
location ~ \.php$ {
    try_files $uri =404;
    fastcgi_pass unix:/tmp/php-cgi.sock;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;

    # 修复 "No input file specified" 错误
    fastcgi_param REQUEST_URI $request_uri;
    fastcgi_param QUERY_STRING $query_string;
    fastcgi_param REQUEST_METHOD $request_method;
    fastcgi_param CONTENT_TYPE $content_type;
    fastcgi_param CONTENT_LENGTH $content_length;
}

# URL重写规则（如果需要）
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

# 安全配置
location ~ /\. {
    deny all;
}

location ~ \.(log|sql|bak)$ {
    deny all;
}